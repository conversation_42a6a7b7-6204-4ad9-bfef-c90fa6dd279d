# Bright Chat Service - API Documentation

## 📚 **Swagger/OpenAPI Documentation**

### 🌐 **Access Swagger UI**

<PERSON>u khi khởi động ứng dụng, bạn có thể truy cập Swagger UI tại:

- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/api-docs
- **OpenAPI YAML**: http://localhost:8080/api-docs.yaml

### 🔑 **Authentication**

Hiện tại API sử dụng header `X-User-Id` để xác thực user. Trong Swagger UI:

1. Click vào nút **"Authorize"** 🔒
2. Nhập User ID vào field **"Value"**
3. Click **"Authorize"**

> **Lưu ý**: Đây là phương thức authentication tạm thời. Trong production nên sử dụng JWT tokens.

### 📋 **API Categories**

#### **1. Messages** 💬
- `POST /api/messages/send` - G<PERSON>i tin nhắn
- `GET /api/messages/conversation/{id}` - Lấy tin nhắn trong cuộc trò chuyện
- `PUT /api/messages/{id}/edit` - Chỉnh sửa tin nhắn
- `DELETE /api/messages/{id}` - Xóa tin nhắn
- `POST /api/messages/{id}/read` - Đánh dấu đã đọc
- `POST /api/messages/{id}/reaction` - Thêm reaction
- `POST /api/messages/{id}/forward` - Forward tin nhắn

#### **2. Conversations** 💭
- `POST /api/conversations` - Tạo cuộc trò chuyện mới
- `GET /api/conversations` - Lấy danh sách cuộc trò chuyện
- `GET /api/conversations/{id}` - Lấy thông tin cuộc trò chuyện
- `POST /api/conversations/{id}/participants` - Thêm thành viên
- `DELETE /api/conversations/{id}/participants/{userId}` - Xóa thành viên
- `POST /api/conversations/{id}/leave` - Rời khỏi cuộc trò chuyện

#### **3. Groups** 👥
- `POST /api/groups` - Tạo nhóm mới
- `GET /api/groups` - Lấy danh sách nhóm
- `GET /api/groups/{id}` - Lấy thông tin nhóm
- `POST /api/groups/{id}/members` - Thêm thành viên vào nhóm
- `DELETE /api/groups/{id}/members/{userId}` - Xóa thành viên khỏi nhóm
- `POST /api/groups/{id}/invite-link` - Tạo link mời
- `POST /api/groups/join/{inviteCode}` - Tham gia nhóm bằng mã mời

#### **4. Chat Users** 👤
- `POST /api/chat-users/initialize` - Khởi tạo chat user
- `GET /api/chat-users/profile` - Lấy thông tin profile
- `PUT /api/chat-users/preferences` - Cập nhật preferences
- `POST /api/chat-users/status/online` - Đặt trạng thái online
- `POST /api/chat-users/block/{userId}` - Block user
- `GET /api/chat-users/search` - Tìm kiếm user

#### **5. Notifications** 🔔
- `GET /api/notifications` - Lấy danh sách thông báo
- `GET /api/notifications/unread` - Lấy thông báo chưa đọc
- `POST /api/notifications/{id}/read` - Đánh dấu đã đọc
- `POST /api/notifications/read-all` - Đánh dấu tất cả đã đọc

### 🚀 **Cách sử dụng Swagger UI**

#### **1. Khởi động ứng dụng**
```bash
mvn spring-boot:run
```

#### **2. Truy cập Swagger UI**
Mở browser và truy cập: http://localhost:8080/swagger-ui.html

#### **3. Authorize với User ID**
- Click nút **"Authorize"**
- Nhập User ID (ví dụ: `user123`)
- Click **"Authorize"**

#### **4. Test API endpoints**
- Chọn endpoint muốn test
- Click **"Try it out"**
- Điền thông tin request
- Click **"Execute"**

### 📝 **Ví dụ sử dụng**

#### **Tạo cuộc trò chuyện mới**
```json
POST /api/conversations
{
  "type": "DIRECT",
  "participantIds": ["user456"]
}
```

#### **Gửi tin nhắn**
```json
POST /api/messages/send
{
  "conversationId": "conv123",
  "text": "Hello, how are you?",
  "type": "TEXT"
}
```

#### **Tạo nhóm chat**
```json
POST /api/groups
{
  "name": "My Group",
  "description": "A group for friends",
  "type": "PRIVATE",
  "privacy": "PRIVATE",
  "memberIds": ["user456", "user789"]
}
```

### 🔧 **Cấu hình Swagger**

Swagger được cấu hình trong:
- `SwaggerConfig.java` - Cấu hình chính
- `application.properties` - Cài đặt SpringDoc

### 📊 **Response Format**

Tất cả API responses đều sử dụng format thống nhất:

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00"
}
```

### ⚠️ **Lưu ý quan trọng**

1. **Authentication**: Hiện tại sử dụng `X-User-Id` header, cần thay thế bằng JWT trong production
2. **Validation**: Tất cả request đều được validate, kiểm tra error messages trong response
3. **Rate Limiting**: Chưa implement, nên thêm trong production
4. **CORS**: Đã cấu hình cho development, cần điều chỉnh cho production

### 🐛 **Troubleshooting**

#### **Swagger UI không load**
- Kiểm tra ứng dụng đã khởi động thành công
- Truy cập http://localhost:8080/api-docs để kiểm tra OpenAPI spec

#### **API trả về 403 Forbidden**
- Kiểm tra đã authorize với User ID chưa
- Kiểm tra User ID có tồn tại trong User Service không

#### **Validation errors**
- Kiểm tra request body format
- Đảm bảo tất cả required fields đều có giá trị

### 📚 **Tài liệu bổ sung**

- [SpringDoc OpenAPI Documentation](https://springdoc.org/)
- [Swagger UI Documentation](https://swagger.io/tools/swagger-ui/)
- [OpenAPI Specification](https://spec.openapis.org/oas/v3.0.3)
