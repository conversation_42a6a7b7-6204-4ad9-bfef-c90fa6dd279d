# CORS Configuration Guide

## 🌐 **Cross-Origin Resource Sharing (CORS) Setup**

### 📋 **Overview**

CORS đã được cấu hình để cho phép frontend applications giao tiếp với Chat Service API một cách an toàn. Configuration bao gồm:

- **REST API CORS**: Cho HTTP requests
- **WebSocket CORS**: Cho real-time messaging
- **Environment-specific settings**: Dev, Test, Production
- **Custom CORS filter**: Cho advanced control

### ⚙️ **Configuration Files**

#### **1. Main CORS Config (`CorsConfig.java`)**
```java
// Global CORS configuration
@Configuration
public class CorsConfig implements WebMvcConfigurer
```

#### **2. WebSocket CORS (`WebSocketConfig.java`)**
```java
// WebSocket STOMP endpoint CORS
registry.addEndpoint("/ws")
    .setAllowedOriginPatterns(allowedOrigins)
```

#### **3. Custom CORS Filter (`CorsFilter.java`)**
```java
// Advanced CORS handling
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class CorsFilter implements Filter
```

### 🔧 **Environment Configurations**

#### **Development (`application-dev.properties`)**
```properties
# Permissive CORS for development
cors.allowed-origins=http://localhost:3000,http://localhost:3001,http://localhost:4200
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH,HEAD
cors.allowed-headers=*
cors.allow-credentials=true
```

#### **Production (`application-prod.properties`)**
```properties
# Restrictive CORS for production
cors.allowed-origins=https://bright-chat.com,https://www.bright-chat.com
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
cors.allowed-headers=Content-Type,Authorization,X-User-Id,X-Requested-With
cors.allow-credentials=true
```

#### **Test (`application-test.properties`)**
```properties
# Open CORS for testing
cors.allowed-origins=*
cors.allowed-methods=*
cors.allowed-headers=*
```

### 🎯 **Supported Origins**

#### **Development**
- `http://localhost:3000` - React development server
- `http://localhost:3001` - Alternative React port
- `http://localhost:4200` - Angular development server
- `http://localhost:8080` - Spring Boot server
- `http://localhost:8081` - User Service

#### **Production**
- `https://bright-chat.com` - Main domain
- `https://www.bright-chat.com` - WWW subdomain
- `https://app.bright-chat.com` - App subdomain
- `https://chat.bright-chat.com` - Chat subdomain

### 📡 **Supported Methods**

- `GET` - Retrieve data
- `POST` - Create new resources
- `PUT` - Update existing resources
- `DELETE` - Remove resources
- `OPTIONS` - CORS preflight requests
- `PATCH` - Partial updates

### 🔑 **Supported Headers**

#### **Request Headers**
- `Content-Type` - Request content type
- `Authorization` - Authentication token
- `X-User-Id` - User identification (temporary)
- `X-Requested-With` - AJAX request indicator
- `Accept` - Response content type
- `Origin` - Request origin
- `X-Custom-Header` - Custom application headers

#### **Exposed Response Headers**
- `X-Total-Count` - Total number of items
- `X-Page-Count` - Number of pages
- `X-User-Id` - User identification
- `Authorization` - Auth token refresh
- `Content-Disposition` - File download info

### 🚀 **Testing CORS Configuration**

#### **1. Start the application**
```bash
mvn spring-boot:run
```

#### **2. Test endpoints**
```bash
# Simple GET test
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: X-User-Id" \
     -X OPTIONS \
     http://localhost:8080/api/cors-test/simple

# POST test with body
curl -H "Origin: http://localhost:3000" \
     -H "Content-Type: application/json" \
     -H "X-User-Id: user123" \
     -X POST \
     -d '{"test": "data"}' \
     http://localhost:8080/api/cors-test/with-body
```

#### **3. Browser testing**
```javascript
// Frontend JavaScript test
fetch('http://localhost:8080/api/cors-test/simple', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'X-User-Id': 'user123'
  },
  credentials: 'include'
})
.then(response => response.json())
.then(data => console.log('CORS test successful:', data))
.catch(error => console.error('CORS test failed:', error));
```

### 🔒 **Security Considerations**

#### **Production Security**
1. **Restrict Origins**: Only allow trusted domains
2. **Limit Methods**: Only allow necessary HTTP methods
3. **Control Headers**: Restrict allowed/exposed headers
4. **Credentials**: Carefully control credential sharing
5. **Monitoring**: Log CORS violations

#### **Development vs Production**
```properties
# Development - Permissive
cors.allowed-origins=http://localhost:*

# Production - Restrictive  
cors.allowed-origins=https://yourdomain.com,https://www.yourdomain.com
```

### 🐛 **Troubleshooting**

#### **Common CORS Errors**

1. **"Access to fetch blocked by CORS policy"**
   - Check if origin is in allowed-origins list
   - Verify method is in allowed-methods
   - Check if headers are allowed

2. **"Preflight request doesn't pass"**
   - Ensure OPTIONS method is allowed
   - Check Access-Control-Request-Headers
   - Verify max-age setting

3. **"Credentials not allowed"**
   - Set `cors.allow-credentials=true`
   - Ensure origin is specific (not *)
   - Check frontend credentials setting

#### **Debug Steps**

1. **Check server logs**
```bash
# Look for CORS filter logs
grep "CORS" logs/bright-chat.log
```

2. **Test with curl**
```bash
# Preflight request
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type,X-User-Id" \
     -X OPTIONS \
     http://localhost:8080/api/messages/send
```

3. **Browser DevTools**
   - Check Network tab for preflight requests
   - Look at Response headers
   - Check Console for CORS errors

### 📝 **Configuration Examples**

#### **React Frontend**
```javascript
// axios configuration
const api = axios.create({
  baseURL: 'http://localhost:8080/api',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'X-User-Id': getCurrentUserId()
  }
});
```

#### **Angular Frontend**
```typescript
// HTTP interceptor
@Injectable()
export class CorsInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const corsRequest = req.clone({
      setHeaders: {
        'X-User-Id': this.authService.getCurrentUserId()
      }
    });
    return next.handle(corsRequest);
  }
}
```

#### **Vue.js Frontend**
```javascript
// Vue axios plugin
Vue.prototype.$http = axios.create({
  baseURL: process.env.VUE_APP_API_URL,
  withCredentials: true,
  headers: {
    'X-User-Id': store.getters.currentUserId
  }
});
```

### 🔄 **WebSocket CORS**

#### **Frontend WebSocket Connection**
```javascript
// SockJS connection with CORS
const socket = new SockJS('http://localhost:8080/ws', null, {
  transports: ['websocket', 'xhr-streaming', 'xhr-polling']
});

const stompClient = Stomp.over(socket);
stompClient.connect({
  'X-User-Id': getCurrentUserId()
}, function(frame) {
  console.log('Connected: ' + frame);
});
```

### ⚠️ **Important Notes**

1. **Remove CORS Test Controller** in production
2. **Monitor CORS violations** for security
3. **Update origins** when deploying to new domains
4. **Test thoroughly** after configuration changes
5. **Use HTTPS** in production for security

### 📚 **References**

- [MDN CORS Documentation](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
- [Spring CORS Documentation](https://docs.spring.io/spring-framework/docs/current/reference/html/web.html#mvc-cors)
- [CORS Best Practices](https://web.dev/cross-origin-resource-sharing/)
