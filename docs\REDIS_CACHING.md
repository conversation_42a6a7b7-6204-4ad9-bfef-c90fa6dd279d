# Redis Caching Implementation

## 🚀 **Overview**

Redis caching đã được implement để cache thông tin user từ User Service, giảm API calls và cải thiện performance đáng kể.

## 📦 **Dependencies Added**

```xml
<!-- Redis Cache -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>

<!-- Redis connection pool -->
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-pool2</artifactId>
</dependency>

<!-- Cache abstraction -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-cache</artifactId>
</dependency>
```

## ⚙️ **Configuration**

### **RedisConfig.java**
- Redis connection factory với Lettuce
- RedisTemplate với JSON serialization
- CacheManager với different TTL cho từng cache
- Connection pooling configuration

### **Cache Names & TTL**
- **`users`**: 1 hour TTL - Individual user data
- **`usersBatch`**: 30 minutes TTL - Batch user queries
- **`userSearch`**: 15 minutes TTL - User search results
- **`userValidation`**: 5 minutes TTL - User existence checks

## 🎯 **Cached Methods**

### **UserServiceClient.java**

#### **1. getUserById(String userId)**
```java
@Cacheable(value = "users", key = "#userId", unless = "#result.isEmpty()")
public Optional<UserInfo> getUserById(String userId)
```
- **Cache Key**: `users::userId`
- **TTL**: 1 hour
- **Condition**: Only cache if user found

#### **2. getUsersByIds(List<String> userIds)**
```java
@Cacheable(value = "usersBatch", key = "#userIds.hashCode()", unless = "#result.isEmpty()")
public List<UserInfo> getUsersByIds(List<String> userIds)
```
- **Cache Key**: `usersBatch::hashCode`
- **TTL**: 30 minutes
- **Condition**: Only cache if users found

#### **3. searchUsers(String query, int limit)**
```java
@Cacheable(value = "userSearch", key = "#query + '_' + #limit", unless = "#result.isEmpty()")
public List<UserInfo> searchUsers(String query, int limit)
```
- **Cache Key**: `userSearch::query_limit`
- **TTL**: 15 minutes
- **Condition**: Only cache if results found

#### **4. userExists(String userId)**
```java
@Cacheable(value = "userValidation", key = "'exists_' + #userId")
public boolean userExists(String userId)
```
- **Cache Key**: `userValidation::exists_userId`
- **TTL**: 5 minutes
- **Always cached**: Even false results

#### **5. validateUserIds(List<String> userIds)**
```java
@Cacheable(value = "userValidation", key = "'validate_' + #userIds.hashCode()")
public List<String> validateUserIds(List<String> userIds)
```
- **Cache Key**: `userValidation::validate_hashCode`
- **TTL**: 5 minutes

## 🔧 **Cache Management**

### **CacheService.java**
- Cache statistics và monitoring
- Manual cache operations
- Redis info và key management
- TTL management

### **CacheController.java**
- REST endpoints để manage cache
- Cache statistics
- Clear cache operations
- Key inspection

## 📊 **Cache Management Endpoints**

### **Statistics**
```bash
GET /api/cache/stats
```

### **Clear Operations**
```bash
# Clear specific cache
DELETE /api/cache/{cacheName}

# Clear all caches
DELETE /api/cache/all

# Evict user cache
DELETE /api/cache/user/{userId}
```

### **Inspection**
```bash
# Get cache entry
GET /api/cache/{cacheName}/{key}

# Get Redis keys
GET /api/cache/keys?pattern=users::*

# Get TTL
GET /api/cache/ttl/{key}
```

### **Warm Up**
```bash
# Warm up user cache
POST /api/cache/warmup/user/{userId}
```

## 🌐 **Environment Configuration**

### **Development**
```properties
# Local Redis
spring.redis.host=localhost
spring.redis.port=6379
spring.cache.redis.time-to-live=600000  # 10 minutes
```

### **Production**
```properties
# Environment variables
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.password=${REDIS_PASSWORD:}
spring.cache.redis.time-to-live=3600000  # 1 hour
```

## 🚀 **Performance Benefits**

### **Before Caching**
- Every user lookup = API call to User Service
- High latency cho repeated requests
- User Service load cao

### **After Caching**
- First lookup = API call + cache store
- Subsequent lookups = Redis cache (sub-millisecond)
- 90%+ reduction trong User Service calls

## 📈 **Usage Examples**

### **Basic Usage**
```java
// First call - hits User Service + caches
Optional<UserInfo> user1 = userServiceClient.getUserById("user123");

// Second call - hits Redis cache only
Optional<UserInfo> user2 = userServiceClient.getUserById("user123");
```

### **Cache Eviction**
```java
// Manual cache eviction
userServiceClient.evictUserCache("user123");

// Clear all user caches
userServiceClient.evictAllUserCaches();
```

### **Cache Inspection**
```java
// Get cache statistics
Map<String, Object> stats = cacheService.getCacheStatistics();

// Check if key exists
boolean exists = cacheService.keyExists("users::user123");

// Get TTL
long ttl = cacheService.getTtl("users::user123");
```

## 🔍 **Monitoring & Debugging**

### **Cache Hit/Miss Logging**
```java
// Enable debug logging
logging.level.com.bright.chat.service.UserServiceClient=DEBUG
```

### **Redis Monitoring**
```bash
# Redis CLI
redis-cli monitor

# Check cache keys
redis-cli keys "users::*"

# Get cache value
redis-cli get "users::user123"
```

### **Application Metrics**
```bash
# Cache statistics endpoint
curl http://localhost:8080/api/cache/stats

# Redis info
curl http://localhost:8080/api/cache/stats | jq '.data.redisInfo'
```

## ⚠️ **Important Notes**

### **Cache Invalidation**
- User data changes trong User Service không tự động invalidate cache
- Cần implement cache eviction strategy
- Consider using Redis pub/sub cho real-time invalidation

### **Memory Management**
- Monitor Redis memory usage
- Set appropriate TTL values
- Consider LRU eviction policy

### **Error Handling**
- Cache failures không affect application functionality
- Fallback to User Service nếu Redis down
- Graceful degradation

## 🔧 **Setup Instructions**

### **1. Install Redis**
```bash
# Docker
docker run -d -p 6379:6379 redis:alpine

# Local installation
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis
```

### **2. Start Application**
```bash
# With dev profile
mvn spring-boot:run -Dspring.profiles.active=dev
```

### **3. Test Caching**
```bash
# First call (cache miss)
curl -H "X-User-Id: user123" http://localhost:8080/api/chat-users/search?query=john

# Second call (cache hit)
curl -H "X-User-Id: user123" http://localhost:8080/api/chat-users/search?query=john

# Check cache stats
curl http://localhost:8080/api/cache/stats
```

## 🎯 **Best Practices**

1. **Cache Key Design**: Use meaningful, unique keys
2. **TTL Strategy**: Balance between freshness và performance
3. **Error Handling**: Always have fallback mechanisms
4. **Monitoring**: Track cache hit rates và performance
5. **Memory Management**: Monitor Redis memory usage
6. **Security**: Secure Redis instance trong production

## 📚 **References**

- [Spring Boot Redis Documentation](https://docs.spring.io/spring-boot/docs/current/reference/html/data.html#data.nosql.redis)
- [Spring Cache Abstraction](https://docs.spring.io/spring-framework/docs/current/reference/html/integration.html#cache)
- [Redis Best Practices](https://redis.io/docs/manual/patterns/)
