package com.bright.chat.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger/OpenAPI configuration for Chat Service
 */
@Configuration
public class SwaggerConfig {

    @Value("${app.name:Bright Chat Service}")
    private String appName;

    @Value("${app.version:1.0.0}")
    private String appVersion;

    @Value("${server.port:8080}")
    private String serverPort;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("Development Server"),
                        new Server()
                                .url("https://api.bright-chat.com")
                                .description("Production Server")
                ))
                .addSecurityItem(new SecurityRequirement().addList("User-ID Header"))
                .components(new io.swagger.v3.oas.models.Components()
                        .addSecuritySchemes("User-ID Header", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.APIKEY)
                                        .in(SecurityScheme.In.HEADER)
                                        .name("X-User-Id")
                                        .description("User ID for authentication (temporary - replace with JWT)")
                        )
                );
    }

    private Info apiInfo() {
        return new Info()
                .title(appName + " API")
                .description("REST API documentation for Bright Chat Service - A microservice for real-time messaging, group management, and notifications")
                .version(appVersion)
                .contact(new Contact()
                        .name("Bright Team")
                        .email("<EMAIL>")
                        .url("https://bright-chat.com"))
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT"));
    }
}
