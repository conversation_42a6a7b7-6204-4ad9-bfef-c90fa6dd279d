package com.bright.chat.controller;

import com.bright.chat.dto.response.ApiResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.view.RedirectView;

import java.util.Map;

/**
 * Controller for API information and documentation
 */
@Tag(name = "API Info", description = "API information and documentation endpoints")
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
public class ApiInfoController {

    @Value("${app.name:Bright Chat Service}")
    private String appName;

    @Value("${app.version:1.0.0}")
    private String appVersion;

    @Value("${server.port:8080}")
    private String serverPort;

    /**
     * Get API information
     */
    @Operation(
            summary = "Get API information",
            description = "Returns basic information about the Chat Service API"
    )
    @GetMapping("/info")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getApiInfo() {
        Map<String, Object> info = Map.of(
                "name", appName,
                "version", appVersion,
                "description", "Microservice for real-time messaging, group management, and notifications",
                "documentation", Map.of(
                        "swagger-ui", "http://localhost:" + serverPort + "/swagger-ui.html",
                        "openapi-json", "http://localhost:" + serverPort + "/api-docs",
                        "openapi-yaml", "http://localhost:" + serverPort + "/api-docs.yaml"
                ),
                "endpoints", Map.of(
                        "messages", "/api/messages",
                        "conversations", "/api/conversations", 
                        "groups", "/api/groups",
                        "chat-users", "/api/chat-users",
                        "notifications", "/api/notifications"
                ),
                "websocket", Map.of(
                        "endpoint", "/ws",
                        "topics", Map.of(
                                "conversation", "/topic/conversation/{conversationId}",
                                "typing", "/topic/conversation/{conversationId}/typing",
                                "reactions", "/topic/conversation/{conversationId}/reaction"
                        )
                ),
                "authentication", Map.of(
                        "type", "Header-based (temporary)",
                        "header", "X-User-Id",
                        "note", "Replace with JWT in production"
                )
        );

        return ResponseEntity.ok(ApiResponse.success("API information retrieved successfully", info));
    }

    /**
     * Redirect to Swagger UI
     */
    @Hidden // Hide from Swagger documentation
    @GetMapping("/docs")
    public RedirectView redirectToSwagger() {
        return new RedirectView("/swagger-ui.html");
    }

    /**
     * Health check endpoint
     */
    @Operation(
            summary = "Health check",
            description = "Simple health check endpoint to verify service is running"
    )
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<Map<String, String>>> healthCheck() {
        Map<String, String> health = Map.of(
                "status", "UP",
                "service", appName,
                "version", appVersion,
                "timestamp", java.time.LocalDateTime.now().toString()
        );

        return ResponseEntity.ok(ApiResponse.success("Service is healthy", health));
    }
}
