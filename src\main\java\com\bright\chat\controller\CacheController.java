package com.bright.chat.controller;

import com.bright.chat.dto.response.ApiResponse;
import com.bright.chat.service.CacheService;
import com.bright.chat.service.UserServiceClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Set;

/**
 * Controller for cache management operations
 * Should be secured in production
 */
@Tag(name = "Cache Management", description = "Endpoints for managing Redis cache (Admin only)")
@RestController
@RequestMapping("/api/cache")
@RequiredArgsConstructor
@Slf4j
public class CacheController {

    private final CacheService cacheService;
    private final UserServiceClient userServiceClient;

    /**
     * Get cache statistics
     */
    @Operation(
            summary = "Get cache statistics",
            description = "Returns statistics about all caches and Redis connection"
    )
    @GetMapping("/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCacheStats() {
        try {
            Map<String, Object> stats = cacheService.getCacheStatistics();
            Map<String, Object> redisInfo = cacheService.getRedisInfo();
            
            Map<String, Object> response = Map.of(
                    "cacheStats", stats,
                    "redisInfo", redisInfo,
                    "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(ApiResponse.success("Cache statistics retrieved", response));
        } catch (Exception e) {
            log.error("Error getting cache stats", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get cache stats: " + e.getMessage()));
        }
    }

    /**
     * Clear specific cache
     */
    @Operation(
            summary = "Clear specific cache",
            description = "Clear all entries in a specific cache"
    )
    @DeleteMapping("/{cacheName}")
    public ResponseEntity<ApiResponse<String>> clearCache(
            @Parameter(description = "Cache name to clear", example = "users")
            @PathVariable String cacheName) {
        
        try {
            boolean cleared = cacheService.clearCache(cacheName);
            if (cleared) {
                return ResponseEntity.ok(ApiResponse.success("Cache cleared successfully: " + cacheName));
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("Cache not found: " + cacheName));
            }
        } catch (Exception e) {
            log.error("Error clearing cache: {}", cacheName, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to clear cache: " + e.getMessage()));
        }
    }

    /**
     * Clear all caches
     */
    @Operation(
            summary = "Clear all caches",
            description = "Clear all cache entries across all caches"
    )
    @DeleteMapping("/all")
    public ResponseEntity<ApiResponse<String>> clearAllCaches() {
        try {
            cacheService.clearAllCaches();
            return ResponseEntity.ok(ApiResponse.success("All caches cleared successfully"));
        } catch (Exception e) {
            log.error("Error clearing all caches", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to clear all caches: " + e.getMessage()));
        }
    }

    /**
     * Get cache entry
     */
    @Operation(
            summary = "Get cache entry",
            description = "Retrieve a specific cache entry by key"
    )
    @GetMapping("/{cacheName}/{key}")
    public ResponseEntity<ApiResponse<Object>> getCacheEntry(
            @Parameter(description = "Cache name", example = "users")
            @PathVariable String cacheName,
            @Parameter(description = "Cache key", example = "user123")
            @PathVariable String key) {
        
        try {
            Object value = cacheService.getCacheEntry(cacheName, key);
            if (value != null) {
                return ResponseEntity.ok(ApiResponse.success("Cache entry found", value));
            } else {
                return ResponseEntity.ok(ApiResponse.success("Cache entry not found", null));
            }
        } catch (Exception e) {
            log.error("Error getting cache entry: {}:{}", cacheName, key, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get cache entry: " + e.getMessage()));
        }
    }

    /**
     * Evict specific cache entry
     */
    @Operation(
            summary = "Evict cache entry",
            description = "Remove a specific cache entry by key"
    )
    @DeleteMapping("/{cacheName}/{key}")
    public ResponseEntity<ApiResponse<String>> evictCacheEntry(
            @Parameter(description = "Cache name", example = "users")
            @PathVariable String cacheName,
            @Parameter(description = "Cache key", example = "user123")
            @PathVariable String key) {
        
        try {
            cacheService.evictCacheEntry(cacheName, key);
            return ResponseEntity.ok(ApiResponse.success("Cache entry evicted: " + cacheName + ":" + key));
        } catch (Exception e) {
            log.error("Error evicting cache entry: {}:{}", cacheName, key, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to evict cache entry: " + e.getMessage()));
        }
    }

    /**
     * Evict user cache
     */
    @Operation(
            summary = "Evict user cache",
            description = "Remove all cache entries for a specific user"
    )
    @DeleteMapping("/user/{userId}")
    public ResponseEntity<ApiResponse<String>> evictUserCache(
            @Parameter(description = "User ID", example = "user123")
            @PathVariable String userId) {
        
        try {
            userServiceClient.evictUserCache(userId);
            return ResponseEntity.ok(ApiResponse.success("User cache evicted: " + userId));
        } catch (Exception e) {
            log.error("Error evicting user cache: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to evict user cache: " + e.getMessage()));
        }
    }

    /**
     * Get Redis keys by pattern
     */
    @Operation(
            summary = "Get Redis keys",
            description = "Get all Redis keys matching a pattern"
    )
    @GetMapping("/keys")
    public ResponseEntity<ApiResponse<Set<String>>> getKeys(
            @Parameter(description = "Key pattern", example = "users::*")
            @RequestParam(defaultValue = "*") String pattern) {
        
        try {
            Set<String> keys = cacheService.getKeys(pattern);
            return ResponseEntity.ok(ApiResponse.success("Keys retrieved", keys));
        } catch (Exception e) {
            log.error("Error getting keys with pattern: {}", pattern, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get keys: " + e.getMessage()));
        }
    }

    /**
     * Get TTL for a key
     */
    @Operation(
            summary = "Get key TTL",
            description = "Get time-to-live for a Redis key in seconds"
    )
    @GetMapping("/ttl/{key}")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getKeyTtl(
            @Parameter(description = "Redis key", example = "users::user123")
            @PathVariable String key) {
        
        try {
            long ttl = cacheService.getTtl(key);
            boolean exists = cacheService.keyExists(key);
            
            Map<String, Object> response = Map.of(
                    "key", key,
                    "exists", exists,
                    "ttl", ttl,
                    "ttlDescription", getTtlDescription(ttl)
            );
            
            return ResponseEntity.ok(ApiResponse.success("TTL retrieved", response));
        } catch (Exception e) {
            log.error("Error getting TTL for key: {}", key, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get TTL: " + e.getMessage()));
        }
    }

    /**
     * Warm up user cache
     */
    @Operation(
            summary = "Warm up user cache",
            description = "Pre-load user data into cache"
    )
    @PostMapping("/warmup/user/{userId}")
    public ResponseEntity<ApiResponse<String>> warmUpUserCache(
            @Parameter(description = "User ID", example = "user123")
            @PathVariable String userId) {
        
        try {
            // Trigger cache loading by calling the cached method
            userServiceClient.getUserById(userId);
            return ResponseEntity.ok(ApiResponse.success("User cache warmed up: " + userId));
        } catch (Exception e) {
            log.error("Error warming up user cache: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to warm up user cache: " + e.getMessage()));
        }
    }

    /**
     * Get TTL description
     */
    private String getTtlDescription(long ttl) {
        if (ttl == -1) {
            return "Key does not exist";
        } else if (ttl == -2) {
            return "Key exists but has no expiration";
        } else if (ttl > 0) {
            return "Expires in " + ttl + " seconds";
        } else {
            return "Unknown";
        }
    }
}
