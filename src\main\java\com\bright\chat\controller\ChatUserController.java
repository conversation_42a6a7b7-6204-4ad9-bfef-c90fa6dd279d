package com.bright.chat.controller;

import com.bright.chat.dto.request.UpdateChatUserRequest;
import com.bright.chat.dto.response.ApiResponse;
import com.bright.chat.dto.response.ChatUserResponse;
import com.bright.chat.mapper.ChatUserMapper;
import com.bright.chat.model.ChatUser;
import com.bright.chat.model.UserInfo;
import com.bright.chat.service.ChatUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for chat user operations
 */
@Tag(name = "Chat Users", description = "API endpoints for chat user management")
@RestController
@RequestMapping("/api/chat-users")
@RequiredArgsConstructor
@Slf4j
public class ChatUserController {

    private final ChatUserService chatUserService;
    private final ChatUserMapper chatUserMapper;

    /**
     * Initialize or update chat user
     */
    @PostMapping("/initialize")
    public ResponseEntity<ApiResponse<ChatUserResponse>> initializeChatUser(
            @RequestParam(required = false) String fcmToken,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            ChatUser chatUser = chatUserService.createOrUpdateChatUser(userId, fcmToken);
            ChatUserResponse response = chatUserMapper.toResponse(chatUser);
            return ResponseEntity.ok(ApiResponse.success("Chat user initialized successfully", response));
        } catch (Exception e) {
            log.error("Error initializing chat user", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to initialize chat user: " + e.getMessage()));
        }
    }

    /**
     * Get current chat user profile
     */
    @GetMapping("/profile")
    public ResponseEntity<ApiResponse<ChatUserResponse>> getChatUserProfile(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            ChatUser chatUser = chatUserService.getChatUser(userId)
                    .orElseThrow(() -> new RuntimeException("Chat user not found"));
            
            ChatUserResponse response = chatUserMapper.toResponse(chatUser);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting chat user profile", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get chat user profile: " + e.getMessage()));
        }
    }

    /**
     * Update chat user preferences
     */
    @PutMapping("/preferences")
    public ResponseEntity<ApiResponse<String>> updateChatUserPreferences(
            @Valid @RequestBody UpdateChatUserRequest request,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            if (request.getFcmToken() != null) {
                chatUserService.updateFcmToken(userId, request.getFcmToken());
            }
            
            if (request.getStatus() != null) {
                chatUserService.updateUserStatus(userId, request.getStatus());
            }
            
            if (request.getIsOnline() != null) {
                if (request.getIsOnline()) {
                    chatUserService.setUserOnline(userId);
                } else {
                    chatUserService.setUserOffline(userId);
                }
            }
            
            if (request.getAllowNotifications() != null || request.getTimezone() != null) {
                chatUserService.updateChatPreferences(
                        userId, 
                        request.getAllowNotifications() != null ? request.getAllowNotifications() : true,
                        request.getTimezone() != null ? request.getTimezone() : "UTC"
                );
            }
            
            return ResponseEntity.ok(ApiResponse.success("Chat user preferences updated successfully"));
        } catch (Exception e) {
            log.error("Error updating chat user preferences", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update preferences: " + e.getMessage()));
        }
    }

    /**
     * Set user online status
     */
    @PostMapping("/status/online")
    public ResponseEntity<ApiResponse<String>> setOnline(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            chatUserService.setUserOnline(userId);
            return ResponseEntity.ok(ApiResponse.success("User status set to online"));
        } catch (Exception e) {
            log.error("Error setting user online", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to set user online: " + e.getMessage()));
        }
    }

    /**
     * Set user offline status
     */
    @PostMapping("/status/offline")
    public ResponseEntity<ApiResponse<String>> setOffline(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            chatUserService.setUserOffline(userId);
            return ResponseEntity.ok(ApiResponse.success("User status set to offline"));
        } catch (Exception e) {
            log.error("Error setting user offline", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to set user offline: " + e.getMessage()));
        }
    }

    /**
     * Block a user
     */
    @PostMapping("/block/{userToBlock}")
    public ResponseEntity<ApiResponse<String>> blockUser(
            @PathVariable String userToBlock,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            chatUserService.blockUser(userId, userToBlock);
            return ResponseEntity.ok(ApiResponse.success("User blocked successfully"));
        } catch (Exception e) {
            log.error("Error blocking user", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to block user: " + e.getMessage()));
        }
    }

    /**
     * Unblock a user
     */
    @PostMapping("/unblock/{userToUnblock}")
    public ResponseEntity<ApiResponse<String>> unblockUser(
            @PathVariable String userToUnblock,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            chatUserService.unblockUser(userId, userToUnblock);
            return ResponseEntity.ok(ApiResponse.success("User unblocked successfully"));
        } catch (Exception e) {
            log.error("Error unblocking user", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to unblock user: " + e.getMessage()));
        }
    }

    /**
     * Get blocked users list
     */
    @GetMapping("/blocked")
    public ResponseEntity<ApiResponse<List<String>>> getBlockedUsers(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            List<String> blockedUsers = chatUserService.getBlockedUsers(userId);
            return ResponseEntity.ok(ApiResponse.success(blockedUsers));
        } catch (Exception e) {
            log.error("Error getting blocked users", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get blocked users: " + e.getMessage()));
        }
    }

    /**
     * Search users (delegates to User Service)
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<UserInfo>>> searchUsers(
            @RequestParam String query,
            @RequestParam(defaultValue = "20") int limit) {
        
        try {
            List<UserInfo> users = chatUserService.searchUsers(query, limit);
            return ResponseEntity.ok(ApiResponse.success(users));
        } catch (Exception e) {
            log.error("Error searching users", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to search users: " + e.getMessage()));
        }
    }

    /**
     * Sync user display information from User Service
     */
    @PostMapping("/sync")
    public ResponseEntity<ApiResponse<String>> syncUserDisplayInfo(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            chatUserService.syncUserDisplayInfo(userId);
            return ResponseEntity.ok(ApiResponse.success("User display info synced successfully"));
        } catch (Exception e) {
            log.error("Error syncing user display info", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to sync user display info: " + e.getMessage()));
        }
    }

    /**
     * Get user's conversations
     */
    @GetMapping("/conversations")
    public ResponseEntity<ApiResponse<List<String>>> getUserConversations(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            List<String> conversations = chatUserService.getUserConversations(userId);
            return ResponseEntity.ok(ApiResponse.success(conversations));
        } catch (Exception e) {
            log.error("Error getting user conversations", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get user conversations: " + e.getMessage()));
        }
    }

    /**
     * Get user's groups
     */
    @GetMapping("/groups")
    public ResponseEntity<ApiResponse<List<String>>> getUserGroups(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            List<String> groups = chatUserService.getUserGroups(userId);
            return ResponseEntity.ok(ApiResponse.success(groups));
        } catch (Exception e) {
            log.error("Error getting user groups", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get user groups: " + e.getMessage()));
        }
    }
}
