package com.bright.chat.controller;

import com.bright.chat.dto.request.CreateConversationRequest;
import com.bright.chat.dto.response.ApiResponse;
import com.bright.chat.dto.response.ConversationResponse;
import com.bright.chat.mapper.ConversationMapper;
import com.bright.chat.model.Conversation;
import com.bright.chat.model.ConversationType;
import com.bright.chat.service.ConversationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * REST Controller for conversation operations
 */
@Tag(name = "Conversations", description = "API endpoints for conversation management")
@RestController
@RequestMapping("/api/conversations")
@RequiredArgsConstructor
@Slf4j
public class ConversationController {

    private final ConversationService conversationService;
    private final ConversationMapper conversationMapper;

    /**
     * Create a new conversation
     */
    @Operation(
            summary = "Create a new conversation",
            description = "Create a direct conversation (1-on-1) or group conversation"
    )
    @PostMapping
    public ResponseEntity<ApiResponse<ConversationResponse>> createConversation(
            @Valid @RequestBody CreateConversationRequest request,
            @Parameter(description = "User ID", required = true)
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            Conversation conversation;
            
            if (request.getType() == ConversationType.DIRECT) {
                // For direct conversation, ensure only 2 participants
                if (request.getParticipantIds().size() != 1) {
                    return ResponseEntity.badRequest()
                            .body(ApiResponse.error("Direct conversation must have exactly 1 other participant"));
                }
                String otherUserId = request.getParticipantIds().get(0);
                conversation = conversationService.createDirectConversation(userId, otherUserId);
            } else {
                // For group conversation
                conversation = conversationService.createGroupConversation(
                        userId, 
                        request.getName(), 
                        request.getDescription(), 
                        request.getParticipantIds()
                );
            }

            ConversationResponse response = conversationMapper.toResponse(conversation);
            return ResponseEntity.ok(ApiResponse.success("Conversation created successfully", response));
        } catch (Exception e) {
            log.error("Error creating conversation", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create conversation: " + e.getMessage()));
        }
    }

    /**
     * Get user's conversations
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<ConversationResponse>>> getUserConversations(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            List<Conversation> conversations = conversationService.getUserConversations(userId);
            List<ConversationResponse> responses = conversationMapper.toResponses(conversations);
            return ResponseEntity.ok(ApiResponse.success(responses));
        } catch (Exception e) {
            log.error("Error getting user conversations", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get conversations: " + e.getMessage()));
        }
    }

    /**
     * Get conversation by ID
     */
    @GetMapping("/{conversationId}")
    public ResponseEntity<ApiResponse<ConversationResponse>> getConversation(
            @PathVariable String conversationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            // Check if user is participant
            if (!conversationService.isParticipant(conversationId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("Access denied to this conversation"));
            }

            Conversation conversation = conversationService.getConversation(conversationId)
                    .orElseThrow(() -> new RuntimeException("Conversation not found"));
            
            ConversationResponse response = conversationMapper.toResponse(conversation);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting conversation", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get conversation: " + e.getMessage()));
        }
    }

    /**
     * Add participant to conversation
     */
    @PostMapping("/{conversationId}/participants")
    public ResponseEntity<ApiResponse<String>> addParticipant(
            @PathVariable String conversationId,
            @RequestParam String participantId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            conversationService.addParticipant(conversationId, participantId, userId);
            return ResponseEntity.ok(ApiResponse.success("Participant added successfully"));
        } catch (Exception e) {
            log.error("Error adding participant", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to add participant: " + e.getMessage()));
        }
    }

    /**
     * Remove participant from conversation
     */
    @DeleteMapping("/{conversationId}/participants/{participantId}")
    public ResponseEntity<ApiResponse<String>> removeParticipant(
            @PathVariable String conversationId,
            @PathVariable String participantId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            conversationService.removeParticipant(conversationId, participantId, userId);
            return ResponseEntity.ok(ApiResponse.success("Participant removed successfully"));
        } catch (Exception e) {
            log.error("Error removing participant", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to remove participant: " + e.getMessage()));
        }
    }

    /**
     * Leave conversation
     */
    @PostMapping("/{conversationId}/leave")
    public ResponseEntity<ApiResponse<String>> leaveConversation(
            @PathVariable String conversationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            conversationService.removeParticipant(conversationId, userId, userId);
            return ResponseEntity.ok(ApiResponse.success("Left conversation successfully"));
        } catch (Exception e) {
            log.error("Error leaving conversation", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to leave conversation: " + e.getMessage()));
        }
    }

    /**
     * Update conversation name
     */
    @PutMapping("/{conversationId}/name")
    public ResponseEntity<ApiResponse<String>> updateConversationName(
            @PathVariable String conversationId,
            @RequestParam String name,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            conversationService.updateConversationName(conversationId, name, userId);
            return ResponseEntity.ok(ApiResponse.success("Conversation name updated successfully"));
        } catch (Exception e) {
            log.error("Error updating conversation name", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update conversation name: " + e.getMessage()));
        }
    }

    /**
     * Update conversation avatar
     */
    @PutMapping("/{conversationId}/avatar")
    public ResponseEntity<ApiResponse<String>> updateConversationAvatar(
            @PathVariable String conversationId,
            @RequestParam String avatarUrl,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            conversationService.updateConversationAvatar(conversationId, avatarUrl, userId);
            return ResponseEntity.ok(ApiResponse.success("Conversation avatar updated successfully"));
        } catch (Exception e) {
            log.error("Error updating conversation avatar", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update conversation avatar: " + e.getMessage()));
        }
    }

    /**
     * Archive conversation
     */
    @PostMapping("/{conversationId}/archive")
    public ResponseEntity<ApiResponse<String>> archiveConversation(
            @PathVariable String conversationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            conversationService.archiveConversation(conversationId, userId);
            return ResponseEntity.ok(ApiResponse.success("Conversation archived successfully"));
        } catch (Exception e) {
            log.error("Error archiving conversation", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to archive conversation: " + e.getMessage()));
        }
    }

    /**
     * Mute/unmute conversation
     */
    @PostMapping("/{conversationId}/mute")
    public ResponseEntity<ApiResponse<String>> muteConversation(
            @PathVariable String conversationId,
            @RequestParam boolean muted,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            conversationService.muteConversation(conversationId, userId, muted);
            String message = muted ? "Conversation muted successfully" : "Conversation unmuted successfully";
            return ResponseEntity.ok(ApiResponse.success(message));
        } catch (Exception e) {
            log.error("Error muting/unmuting conversation", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update conversation mute status: " + e.getMessage()));
        }
    }

    /**
     * Get conversation statistics
     */
    @GetMapping("/{conversationId}/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getConversationStats(
            @PathVariable String conversationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            // Check if user is participant
            if (!conversationService.isParticipant(conversationId, userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("Access denied to this conversation"));
            }

            Map<String, Object> stats = conversationService.getConversationStats(conversationId);
            return ResponseEntity.ok(ApiResponse.success(stats));
        } catch (Exception e) {
            log.error("Error getting conversation stats", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get conversation stats: " + e.getMessage()));
        }
    }

    /**
     * Delete conversation
     */
    @DeleteMapping("/{conversationId}")
    public ResponseEntity<ApiResponse<String>> deleteConversation(
            @PathVariable String conversationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            conversationService.deleteConversation(conversationId, userId);
            return ResponseEntity.ok(ApiResponse.success("Conversation deleted successfully"));
        } catch (Exception e) {
            log.error("Error deleting conversation", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete conversation: " + e.getMessage()));
        }
    }
}
