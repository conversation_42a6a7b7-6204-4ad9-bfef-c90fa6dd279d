package com.bright.chat.controller;

import com.bright.chat.dto.response.ApiResponse;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * Controller for testing CORS configuration
 * This controller should be removed or secured in production
 */
@Tag(name = "CORS Test", description = "Endpoints for testing CORS configuration (Development only)")
@RestController
@RequestMapping("/api/cors-test")
@Slf4j
public class CorsTestController {

    /**
     * Simple GET endpoint to test CORS
     */
    @Operation(
            summary = "Test CORS GET request",
            description = "Simple endpoint to test CORS configuration for GET requests"
    )
    @GetMapping("/simple")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testSimpleGet(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "CORS GET request successful");
        response.put("origin", request.getHeader("Origin"));
        response.put("method", request.getMethod());
        response.put("timestamp", System.currentTimeMillis());
        
        log.info("CORS test GET request from origin: {}", request.getHeader("Origin"));
        
        return ResponseEntity.ok(ApiResponse.success("CORS test successful", response));
    }

    /**
     * POST endpoint to test CORS with body
     */
    @Operation(
            summary = "Test CORS POST request",
            description = "POST endpoint to test CORS configuration with request body"
    )
    @PostMapping("/with-body")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testPostWithBody(
            @RequestBody Map<String, Object> requestBody,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "CORS POST request successful");
        response.put("origin", request.getHeader("Origin"));
        response.put("method", request.getMethod());
        response.put("receivedData", requestBody);
        response.put("timestamp", System.currentTimeMillis());
        
        log.info("CORS test POST request from origin: {} with body: {}", 
                request.getHeader("Origin"), requestBody);
        
        return ResponseEntity.ok(ApiResponse.success("CORS POST test successful", response));
    }

    /**
     * Endpoint to test custom headers
     */
    @Operation(
            summary = "Test CORS with custom headers",
            description = "Endpoint to test CORS configuration with custom headers"
    )
    @PostMapping("/custom-headers")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testCustomHeaders(
            @RequestHeader(value = "X-Custom-Header", required = false) String customHeader,
            @RequestHeader(value = "X-User-Id", required = false) String userId,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "CORS custom headers test successful");
        response.put("origin", request.getHeader("Origin"));
        response.put("customHeader", customHeader);
        response.put("userId", userId);
        response.put("timestamp", System.currentTimeMillis());
        
        // Add all request headers for debugging
        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        response.put("allHeaders", headers);
        
        log.info("CORS test custom headers request from origin: {}", request.getHeader("Origin"));
        
        return ResponseEntity.ok(ApiResponse.success("CORS custom headers test successful", response));
    }

    /**
     * Endpoint that explicitly handles OPTIONS requests
     */
    @Hidden // Hide from Swagger as it's just for CORS preflight
    @RequestMapping(value = "/preflight", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handlePreflight(HttpServletRequest request) {
        log.info("CORS preflight request from origin: {}", request.getHeader("Origin"));
        return ResponseEntity.ok().build();
    }

    /**
     * Endpoint to test credentials
     */
    @Operation(
            summary = "Test CORS with credentials",
            description = "Endpoint to test CORS configuration with credentials (cookies, authorization headers)"
    )
    @PostMapping("/credentials")
    public ResponseEntity<ApiResponse<Map<String, Object>>> testCredentials(
            @RequestHeader(value = "Authorization", required = false) String authorization,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "CORS credentials test successful");
        response.put("origin", request.getHeader("Origin"));
        response.put("hasAuthorization", authorization != null);
        response.put("cookies", request.getCookies() != null ? request.getCookies().length : 0);
        response.put("timestamp", System.currentTimeMillis());
        
        log.info("CORS test credentials request from origin: {}", request.getHeader("Origin"));
        
        return ResponseEntity.ok(ApiResponse.success("CORS credentials test successful", response));
    }

    /**
     * Endpoint to get CORS configuration info
     */
    @Operation(
            summary = "Get CORS configuration",
            description = "Returns current CORS configuration for debugging"
    )
    @GetMapping("/config")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCorsConfig(HttpServletRequest request) {
        Map<String, Object> config = new HashMap<>();
        config.put("requestOrigin", request.getHeader("Origin"));
        config.put("requestMethod", request.getMethod());
        config.put("userAgent", request.getHeader("User-Agent"));
        config.put("referer", request.getHeader("Referer"));
        config.put("timestamp", System.currentTimeMillis());
        
        // Note: Actual CORS configuration is not exposed for security reasons
        config.put("note", "CORS configuration is managed server-side and not exposed for security");
        
        return ResponseEntity.ok(ApiResponse.success("CORS configuration info", config));
    }
}
