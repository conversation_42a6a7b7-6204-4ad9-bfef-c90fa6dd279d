package com.bright.chat.controller;

import com.bright.chat.dto.request.AddGroupMemberRequest;
import com.bright.chat.dto.request.CreateGroupRequest;
import com.bright.chat.dto.request.UpdateGroupRequest;
import com.bright.chat.dto.response.ApiResponse;
import com.bright.chat.dto.response.GroupResponse;
import com.bright.chat.mapper.GroupMapper;
import com.bright.chat.model.Group;

import com.bright.chat.service.GroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for group operations
 */
@Tag(name = "Groups", description = "API endpoints for group management")
@RestController
@RequestMapping("/api/groups")
@RequiredArgsConstructor
@Slf4j
public class GroupController {

    private final GroupService groupService;
    private final GroupMapper groupMapper;

    /**
     * Create a new group
     */
    @PostMapping
    public ResponseEntity<ApiResponse<GroupResponse>> createGroup(
            @Valid @RequestBody CreateGroupRequest request,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            Group group = groupService.createGroup(
                    userId,
                    request.getName(),
                    request.getDescription(),
                    request.getType(),
                    request.getPrivacy(),
                    request.getMemberIds()
            );

            GroupResponse response = groupMapper.toResponse(group);
            return ResponseEntity.ok(ApiResponse.success("Group created successfully", response));
        } catch (Exception e) {
            log.error("Error creating group", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to create group: " + e.getMessage()));
        }
    }

    /**
     * Get user's groups
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<GroupResponse>>> getUserGroups(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            List<Group> groups = groupService.getUserGroups(userId);
            List<GroupResponse> responses = groupMapper.toResponses(groups);
            return ResponseEntity.ok(ApiResponse.success(responses));
        } catch (Exception e) {
            log.error("Error getting user groups", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get groups: " + e.getMessage()));
        }
    }

    /**
     * Get group by ID
     */
    @GetMapping("/{groupId}")
    public ResponseEntity<ApiResponse<GroupResponse>> getGroup(
            @PathVariable String groupId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            Group group = groupService.getGroup(groupId)
                    .orElseThrow(() -> new RuntimeException("Group not found"));
            
            // Check if user is a member
            if (!group.getMemberIds().contains(userId)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                        .body(ApiResponse.error("Access denied to this group"));
            }

            GroupResponse response = groupMapper.toResponse(group);
            return ResponseEntity.ok(ApiResponse.success(response));
        } catch (Exception e) {
            log.error("Error getting group", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get group: " + e.getMessage()));
        }
    }

    /**
     * Add members to group
     */
    @PostMapping("/{groupId}/members")
    public ResponseEntity<ApiResponse<String>> addMembers(
            @PathVariable String groupId,
            @Valid @RequestBody AddGroupMemberRequest request,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            for (String memberId : request.getUserIds()) {
                groupService.addMember(groupId, memberId, userId);
            }
            return ResponseEntity.ok(ApiResponse.success("Members added successfully"));
        } catch (Exception e) {
            log.error("Error adding members to group", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to add members: " + e.getMessage()));
        }
    }

    /**
     * Remove member from group
     */
    @DeleteMapping("/{groupId}/members/{memberId}")
    public ResponseEntity<ApiResponse<String>> removeMember(
            @PathVariable String groupId,
            @PathVariable String memberId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.removeMember(groupId, memberId, userId);
            return ResponseEntity.ok(ApiResponse.success("Member removed successfully"));
        } catch (Exception e) {
            log.error("Error removing member from group", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to remove member: " + e.getMessage()));
        }
    }

    /**
     * Leave group
     */
    @PostMapping("/{groupId}/leave")
    public ResponseEntity<ApiResponse<String>> leaveGroup(
            @PathVariable String groupId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.leaveGroup(groupId, userId);
            return ResponseEntity.ok(ApiResponse.success("Left group successfully"));
        } catch (Exception e) {
            log.error("Error leaving group", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to leave group: " + e.getMessage()));
        }
    }

    /**
     * Promote member to admin
     */
    @PostMapping("/{groupId}/members/{memberId}/promote")
    public ResponseEntity<ApiResponse<String>> promoteToAdmin(
            @PathVariable String groupId,
            @PathVariable String memberId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.promoteToAdmin(groupId, memberId);
            return ResponseEntity.ok(ApiResponse.success("Member promoted to admin successfully"));
        } catch (Exception e) {
            log.error("Error promoting member to admin", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to promote member: " + e.getMessage()));
        }
    }

    /**
     * Demote admin to member
     */
    @PostMapping("/{groupId}/members/{memberId}/demote")
    public ResponseEntity<ApiResponse<String>> demoteAdmin(
            @PathVariable String groupId,
            @PathVariable String memberId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.demoteAdmin(groupId, memberId);
            return ResponseEntity.ok(ApiResponse.success("Admin demoted to member successfully"));
        } catch (Exception e) {
            log.error("Error demoting admin", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to demote admin: " + e.getMessage()));
        }
    }

    /**
     * Transfer group ownership
     */
    @PostMapping("/{groupId}/transfer-ownership")
    public ResponseEntity<ApiResponse<String>> transferOwnership(
            @PathVariable String groupId,
            @RequestParam String newOwnerId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.transferOwnership(groupId, newOwnerId);
            return ResponseEntity.ok(ApiResponse.success("Ownership transferred successfully"));
        } catch (Exception e) {
            log.error("Error transferring ownership", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to transfer ownership: " + e.getMessage()));
        }
    }

    /**
     * Update group information
     */
    @PutMapping("/{groupId}")
    public ResponseEntity<ApiResponse<String>> updateGroup(
            @PathVariable String groupId,
            @Valid @RequestBody UpdateGroupRequest request,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            if (request.getName() != null || request.getDescription() != null) {
                groupService.updateGroupInfo(groupId, request.getName(), request.getDescription(), userId);
            }
            return ResponseEntity.ok(ApiResponse.success("Group updated successfully"));
        } catch (Exception e) {
            log.error("Error updating group", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to update group: " + e.getMessage()));
        }
    }

    /**
     * Generate invite link
     */
    @PostMapping("/{groupId}/invite-link")
    public ResponseEntity<ApiResponse<String>> generateInviteLink(
            @PathVariable String groupId,
            @RequestParam(defaultValue = "100") int maxUses,
            @RequestParam(defaultValue = "7") int validDays,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            String inviteCode = groupService.generateInviteLink(groupId, maxUses, validDays);
            return ResponseEntity.ok(ApiResponse.success("Invite link generated successfully", inviteCode));
        } catch (Exception e) {
            log.error("Error generating invite link", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to generate invite link: " + e.getMessage()));
        }
    }

    /**
     * Join group by invite code
     */
    @PostMapping("/join/{inviteCode}")
    public ResponseEntity<ApiResponse<String>> joinByInviteCode(
            @PathVariable String inviteCode,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.joinByInviteCode(inviteCode, userId);
            return ResponseEntity.ok(ApiResponse.success("Joined group successfully"));
        } catch (Exception e) {
            log.error("Error joining group by invite code", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to join group: " + e.getMessage()));
        }
    }

    /**
     * Ban user from group
     */
    @PostMapping("/{groupId}/members/{memberId}/ban")
    public ResponseEntity<ApiResponse<String>> banUser(
            @PathVariable String groupId,
            @PathVariable String memberId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.banUser(groupId, memberId, userId);
            return ResponseEntity.ok(ApiResponse.success("User banned successfully"));
        } catch (Exception e) {
            log.error("Error banning user", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to ban user: " + e.getMessage()));
        }
    }

    /**
     * Unban user from group
     */
    @PostMapping("/{groupId}/members/{memberId}/unban")
    public ResponseEntity<ApiResponse<String>> unbanUser(
            @PathVariable String groupId,
            @PathVariable String memberId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.unbanUser(groupId, memberId, userId);
            return ResponseEntity.ok(ApiResponse.success("User unbanned successfully"));
        } catch (Exception e) {
            log.error("Error unbanning user", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to unban user: " + e.getMessage()));
        }
    }

    /**
     * Mute user in group
     */
    @PostMapping("/{groupId}/members/{memberId}/mute")
    public ResponseEntity<ApiResponse<String>> muteUser(
            @PathVariable String groupId,
            @PathVariable String memberId,
            @RequestParam(defaultValue = "60") int durationMinutes,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.muteUser(groupId, memberId, durationMinutes, userId);
            return ResponseEntity.ok(ApiResponse.success("User muted successfully"));
        } catch (Exception e) {
            log.error("Error muting user", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to mute user: " + e.getMessage()));
        }
    }

    /**
     * Unmute user in group
     */
    @PostMapping("/{groupId}/members/{memberId}/unmute")
    public ResponseEntity<ApiResponse<String>> unmuteUser(
            @PathVariable String groupId,
            @PathVariable String memberId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.unmuteUser(groupId, memberId, userId);
            return ResponseEntity.ok(ApiResponse.success("User unmuted successfully"));
        } catch (Exception e) {
            log.error("Error unmuting user", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to unmute user: " + e.getMessage()));
        }
    }

    /**
     * Search public groups
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<GroupResponse>>> searchPublicGroups(
            @RequestParam String query) {
        
        try {
            List<Group> groups = groupService.searchPublicGroups(query);
            List<GroupResponse> responses = groupMapper.toResponses(groups);
            return ResponseEntity.ok(ApiResponse.success(responses));
        } catch (Exception e) {
            log.error("Error searching public groups", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to search groups: " + e.getMessage()));
        }
    }

    /**
     * Delete group
     */
    @DeleteMapping("/{groupId}")
    public ResponseEntity<ApiResponse<String>> deleteGroup(
            @PathVariable String groupId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            groupService.deleteGroup(groupId, userId);
            return ResponseEntity.ok(ApiResponse.success("Group deleted successfully"));
        } catch (Exception e) {
            log.error("Error deleting group", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete group: " + e.getMessage()));
        }
    }
}
