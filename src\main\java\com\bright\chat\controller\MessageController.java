package com.bright.chat.controller;

import com.bright.chat.dto.request.SendMessageRequest;
import com.bright.chat.dto.response.ApiResponse;
import com.bright.chat.dto.response.MessageResponse;
import com.bright.chat.mapper.MessageMapper;
import com.bright.chat.model.Message;
import com.bright.chat.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for message operations
 */
@Tag(name = "Messages", description = "API endpoints for message operations")
@RestController
@RequestMapping("/api/messages")
@RequiredArgsConstructor
@Slf4j
public class MessageController {

    private final MessageService messageService;
    private final MessageMapper messageMapper;

    /**
     * Send a text message
     */
    @Operation(
            summary = "Send a message",
            description = "Send a text message, reply, or message with attachments to a conversation"
    )
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                    responseCode = "200",
                    description = "Message sent successfully",
                    content = @Content(schema = @Schema(implementation = MessageResponse.class))
            ),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(
                    responseCode = "400",
                    description = "Invalid request or user not authorized",
                    content = @Content(schema = @Schema(implementation = ApiResponse.class))
            )
    })
    @PostMapping("/send")
    public ResponseEntity<ApiResponse<MessageResponse>> sendMessage(
            @Valid @RequestBody SendMessageRequest request,
            @Parameter(description = "User ID", required = true)
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            Message message;
            if (request.getReplyToMessageId() != null) {
                message = messageService.replyToMessage(
                        request.getConversationId(),
                        userId,
                        request.getText(),
                        request.getReplyToMessageId()
                );
            } else if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
                message = messageService.sendMessageWithAttachments(
                        request.getConversationId(),
                        userId,
                        request.getText(),
                        request.getType(),
                        messageMapper.toAttachments(request.getAttachments())
                );
            } else {
                message = messageService.sendTextMessage(
                        request.getConversationId(),
                        userId,
                        request.getText()
                );
            }

            MessageResponse response = messageMapper.toResponse(message);
            return ResponseEntity.ok(ApiResponse.success("Message sent successfully", response));
        } catch (Exception e) {
            log.error("Error sending message", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to send message: " + e.getMessage()));
        }
    }

    /**
     * Get messages in a conversation
     */
    @Operation(
            summary = "Get messages in a conversation",
            description = "Retrieve recent messages from a conversation with pagination"
    )
    @GetMapping("/conversation/{conversationId}")
    public ResponseEntity<ApiResponse<List<MessageResponse>>> getMessages(
            @Parameter(description = "Conversation ID", required = true)
            @PathVariable String conversationId,
            @Parameter(description = "User ID", required = true)
            @RequestHeader("X-User-Id") String userId,
            @Parameter(description = "Maximum number of messages to retrieve")
            @RequestParam(defaultValue = "50") int limit) {
        
        try {
            List<Message> messages = messageService.getRecentMessages(conversationId, userId, limit);
            List<MessageResponse> responses = messageMapper.toResponses(messages);
            return ResponseEntity.ok(ApiResponse.success(responses));
        } catch (Exception e) {
            log.error("Error getting messages", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get messages: " + e.getMessage()));
        }
    }

    /**
     * Edit a message
     */
    @PutMapping("/{messageId}/edit")
    public ResponseEntity<ApiResponse<String>> editMessage(
            @PathVariable String messageId,
            @RequestParam String conversationId,
            @RequestParam String newText,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            messageService.editMessage(conversationId, messageId, newText, userId);
            return ResponseEntity.ok(ApiResponse.success("Message edited successfully"));
        } catch (Exception e) {
            log.error("Error editing message", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to edit message: " + e.getMessage()));
        }
    }

    /**
     * Delete a message
     */
    @DeleteMapping("/{messageId}")
    public ResponseEntity<ApiResponse<String>> deleteMessage(
            @PathVariable String messageId,
            @RequestParam String conversationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            messageService.deleteMessage(conversationId, messageId, userId);
            return ResponseEntity.ok(ApiResponse.success("Message deleted successfully"));
        } catch (Exception e) {
            log.error("Error deleting message", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to delete message: " + e.getMessage()));
        }
    }

    /**
     * Mark message as read
     */
    @PostMapping("/{messageId}/read")
    public ResponseEntity<ApiResponse<String>> markAsRead(
            @PathVariable String messageId,
            @RequestParam String conversationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            messageService.markMessageAsRead(conversationId, messageId, userId);
            return ResponseEntity.ok(ApiResponse.success("Message marked as read"));
        } catch (Exception e) {
            log.error("Error marking message as read", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to mark message as read: " + e.getMessage()));
        }
    }

    /**
     * Add reaction to message
     */
    @PostMapping("/{messageId}/reaction")
    public ResponseEntity<ApiResponse<String>> addReaction(
            @PathVariable String messageId,
            @RequestParam String conversationId,
            @RequestParam String emoji,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            messageService.addReaction(conversationId, messageId, emoji, userId);
            return ResponseEntity.ok(ApiResponse.success("Reaction added successfully"));
        } catch (Exception e) {
            log.error("Error adding reaction", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to add reaction: " + e.getMessage()));
        }
    }

    /**
     * Remove reaction from message
     */
    @DeleteMapping("/{messageId}/reaction")
    public ResponseEntity<ApiResponse<String>> removeReaction(
            @PathVariable String messageId,
            @RequestParam String conversationId,
            @RequestParam String emoji,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            messageService.removeReaction(conversationId, messageId, emoji, userId);
            return ResponseEntity.ok(ApiResponse.success("Reaction removed successfully"));
        } catch (Exception e) {
            log.error("Error removing reaction", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to remove reaction: " + e.getMessage()));
        }
    }

    /**
     * Forward message to another conversation
     */
    @PostMapping("/{messageId}/forward")
    public ResponseEntity<ApiResponse<MessageResponse>> forwardMessage(
            @PathVariable String messageId,
            @RequestParam String originalConversationId,
            @RequestParam String targetConversationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            Message forwardedMessage = messageService.forwardMessage(
                    originalConversationId, messageId, targetConversationId, userId);
            MessageResponse response = messageMapper.toResponse(forwardedMessage);
            return ResponseEntity.ok(ApiResponse.success("Message forwarded successfully", response));
        } catch (Exception e) {
            log.error("Error forwarding message", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to forward message: " + e.getMessage()));
        }
    }
}
