package com.bright.chat.controller;

import com.bright.chat.dto.response.ApiResponse;
import com.bright.chat.dto.response.NotificationResponse;
import com.bright.chat.mapper.NotificationMapper;
import com.bright.chat.model.Notification;
import com.bright.chat.service.NotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for notification operations
 */
@Tag(name = "Notifications", description = "API endpoints for notification management")
@RestController
@RequestMapping("/api/notifications")
@RequiredArgsConstructor
@Slf4j
public class NotificationController {

    private final NotificationService notificationService;
    private final NotificationMapper notificationMapper;

    /**
     * Get user's notifications
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<NotificationResponse>>> getUserNotifications(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            List<Notification> notifications = notificationService.getUserNotifications(userId);
            List<NotificationResponse> responses = notificationMapper.toResponses(notifications);
            return ResponseEntity.ok(ApiResponse.success(responses));
        } catch (Exception e) {
            log.error("Error getting user notifications", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get notifications: " + e.getMessage()));
        }
    }

    /**
     * Get unread notifications
     */
    @GetMapping("/unread")
    public ResponseEntity<ApiResponse<List<NotificationResponse>>> getUnreadNotifications(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            List<Notification> notifications = notificationService.getUnreadNotifications(userId);
            List<NotificationResponse> responses = notificationMapper.toResponses(notifications);
            return ResponseEntity.ok(ApiResponse.success(responses));
        } catch (Exception e) {
            log.error("Error getting unread notifications", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to get unread notifications: " + e.getMessage()));
        }
    }

    /**
     * Mark notification as read
     */
    @PostMapping("/{notificationId}/read")
    public ResponseEntity<ApiResponse<String>> markNotificationAsRead(
            @PathVariable String notificationId,
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            notificationService.markNotificationAsRead(notificationId);
            return ResponseEntity.ok(ApiResponse.success("Notification marked as read"));
        } catch (Exception e) {
            log.error("Error marking notification as read", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to mark notification as read: " + e.getMessage()));
        }
    }

    /**
     * Mark all notifications as read
     */
    @PostMapping("/read-all")
    public ResponseEntity<ApiResponse<String>> markAllNotificationsAsRead(
            @RequestHeader("X-User-Id") String userId) {
        
        try {
            notificationService.markAllNotificationsAsRead(userId);
            return ResponseEntity.ok(ApiResponse.success("All notifications marked as read"));
        } catch (Exception e) {
            log.error("Error marking all notifications as read", e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to mark all notifications as read: " + e.getMessage()));
        }
    }
}
