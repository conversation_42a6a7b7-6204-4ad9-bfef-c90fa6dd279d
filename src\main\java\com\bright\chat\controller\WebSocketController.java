package com.bright.chat.controller;

import com.bright.chat.dto.request.SendMessageRequest;
import com.bright.chat.dto.response.MessageResponse;
import com.bright.chat.mapper.MessageMapper;
import com.bright.chat.model.Message;
import com.bright.chat.service.ChatUserService;
import com.bright.chat.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.util.Map;

/**
 * WebSocket Controller for real-time messaging
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class WebSocketController {

    private final MessageService messageService;
    private final ChatUserService chatUserService;
    private final MessageMapper messageMapper;
    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Handle incoming messages via WebSocket
     */
    @MessageMapping("/conversation/{conversationId}/send")
    @SendTo("/topic/conversation/{conversationId}")
    public MessageResponse sendMessage(
            @DestinationVariable String conversationId,
            @Payload SendMessageRequest request,
            SimpMessageHeaderAccessor headerAccessor,
            Principal principal) {
        
        try {
            String userId = getUserIdFromPrincipal(principal, headerAccessor);
            
            Message message;
            if (request.getReplyToMessageId() != null) {
                message = messageService.replyToMessage(
                        conversationId,
                        userId,
                        request.getText(),
                        request.getReplyToMessageId()
                );
            } else if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
                message = messageService.sendMessageWithAttachments(
                        conversationId,
                        userId,
                        request.getText(),
                        request.getType(),
                        messageMapper.toAttachments(request.getAttachments())
                );
            } else {
                message = messageService.sendTextMessage(conversationId, userId, request.getText());
            }

            return messageMapper.toResponse(message);
        } catch (Exception e) {
            log.error("Error sending message via WebSocket", e);
            // Send error message back to sender
            messagingTemplate.convertAndSendToUser(
                    getUserIdFromPrincipal(principal, headerAccessor),
                    "/queue/errors",
                    Map.of("error", "Failed to send message: " + e.getMessage())
            );
            return null;
        }
    }

    /**
     * Handle typing indicators
     */
    @MessageMapping("/conversation/{conversationId}/typing")
    @SendTo("/topic/conversation/{conversationId}/typing")
    public Map<String, Object> handleTyping(
            @DestinationVariable String conversationId,
            @Payload Map<String, Object> typingData,
            SimpMessageHeaderAccessor headerAccessor,
            Principal principal) {
        
        try {
            String userId = getUserIdFromPrincipal(principal, headerAccessor);
            boolean isTyping = (Boolean) typingData.getOrDefault("isTyping", false);
            
            return Map.of(
                    "userId", userId,
                    "isTyping", isTyping,
                    "timestamp", System.currentTimeMillis()
            );
        } catch (Exception e) {
            log.error("Error handling typing indicator", e);
            return null;
        }
    }

    /**
     * Handle message read receipts
     */
    @MessageMapping("/conversation/{conversationId}/read")
    public void handleMessageRead(
            @DestinationVariable String conversationId,
            @Payload Map<String, String> readData,
            SimpMessageHeaderAccessor headerAccessor,
            Principal principal) {
        
        try {
            String userId = getUserIdFromPrincipal(principal, headerAccessor);
            String messageId = readData.get("messageId");
            
            if (messageId != null) {
                messageService.markMessageAsRead(conversationId, messageId, userId);
                
                // Broadcast read receipt to conversation participants
                messagingTemplate.convertAndSend(
                        "/topic/conversation/" + conversationId + "/read",
                        Map.of(
                                "messageId", messageId,
                                "userId", userId,
                                "timestamp", System.currentTimeMillis()
                        )
                );
            }
        } catch (Exception e) {
            log.error("Error handling message read receipt", e);
        }
    }

    /**
     * Handle user presence (online/offline)
     */
    @MessageMapping("/presence")
    public void handlePresence(
            @Payload Map<String, Object> presenceData,
            SimpMessageHeaderAccessor headerAccessor,
            Principal principal) {
        
        try {
            String userId = getUserIdFromPrincipal(principal, headerAccessor);
            boolean isOnline = (Boolean) presenceData.getOrDefault("isOnline", false);
            
            if (isOnline) {
                chatUserService.setUserOnline(userId);
            } else {
                chatUserService.setUserOffline(userId);
            }
            
            // Broadcast presence update to user's conversations
            // This would require getting user's conversations and broadcasting to each
            // For now, we'll just log the presence change
            log.info("User {} presence changed to: {}", userId, isOnline ? "online" : "offline");
            
        } catch (Exception e) {
            log.error("Error handling presence update", e);
        }
    }

    /**
     * Handle message reactions
     */
    @MessageMapping("/conversation/{conversationId}/reaction")
    @SendTo("/topic/conversation/{conversationId}/reaction")
    public Map<String, Object> handleReaction(
            @DestinationVariable String conversationId,
            @Payload Map<String, String> reactionData,
            SimpMessageHeaderAccessor headerAccessor,
            Principal principal) {
        
        try {
            String userId = getUserIdFromPrincipal(principal, headerAccessor);
            String messageId = reactionData.get("messageId");
            String emoji = reactionData.get("emoji");
            String action = reactionData.get("action"); // "add" or "remove"
            
            if (messageId != null && emoji != null && action != null) {
                if ("add".equals(action)) {
                    messageService.addReaction(conversationId, messageId, emoji, userId);
                } else if ("remove".equals(action)) {
                    messageService.removeReaction(conversationId, messageId, emoji, userId);
                }
                
                return Map.of(
                        "messageId", messageId,
                        "emoji", emoji,
                        "userId", userId,
                        "action", action,
                        "timestamp", System.currentTimeMillis()
                );
            }
        } catch (Exception e) {
            log.error("Error handling reaction", e);
        }
        return null;
    }

    /**
     * Handle join conversation event
     */
    @MessageMapping("/conversation/{conversationId}/join")
    public void handleJoinConversation(
            @DestinationVariable String conversationId,
            SimpMessageHeaderAccessor headerAccessor,
            Principal principal) {
        
        try {
            String userId = getUserIdFromPrincipal(principal, headerAccessor);
            
            // Add user to conversation session
            headerAccessor.getSessionAttributes().put("conversationId", conversationId);
            headerAccessor.getSessionAttributes().put("userId", userId);
            
            log.info("User {} joined conversation {} via WebSocket", userId, conversationId);
            
            // Optionally broadcast join event to other participants
            messagingTemplate.convertAndSend(
                    "/topic/conversation/" + conversationId + "/events",
                    Map.of(
                            "type", "user_joined",
                            "userId", userId,
                            "timestamp", System.currentTimeMillis()
                    )
            );
            
        } catch (Exception e) {
            log.error("Error handling join conversation", e);
        }
    }

    /**
     * Handle leave conversation event
     */
    @MessageMapping("/conversation/{conversationId}/leave")
    public void handleLeaveConversation(
            @DestinationVariable String conversationId,
            SimpMessageHeaderAccessor headerAccessor,
            Principal principal) {
        
        try {
            String userId = getUserIdFromPrincipal(principal, headerAccessor);
            
            // Remove user from conversation session
            headerAccessor.getSessionAttributes().remove("conversationId");
            
            log.info("User {} left conversation {} via WebSocket", userId, conversationId);
            
            // Optionally broadcast leave event to other participants
            messagingTemplate.convertAndSend(
                    "/topic/conversation/" + conversationId + "/events",
                    Map.of(
                            "type", "user_left",
                            "userId", userId,
                            "timestamp", System.currentTimeMillis()
                    )
            );
            
        } catch (Exception e) {
            log.error("Error handling leave conversation", e);
        }
    }

    /**
     * Extract user ID from Principal or session attributes
     */
    private String getUserIdFromPrincipal(Principal principal, SimpMessageHeaderAccessor headerAccessor) {
        // First try to get from Principal (if authentication is implemented)
        if (principal != null) {
            return principal.getName();
        }
        
        // Fallback to session attributes or headers
        // In a real implementation, you would extract this from JWT token or session
        Object userId = headerAccessor.getSessionAttributes().get("userId");
        if (userId != null) {
            return userId.toString();
        }
        
        // For now, we'll expect it to be passed in the message headers
        // This is not secure and should be replaced with proper authentication
        return headerAccessor.getFirstNativeHeader("X-User-Id");
    }
}
