package com.bright.chat.dto.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Request DTO for adding members to a group
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddGroupMemberRequest {

    @NotEmpty(message = "User IDs are required")
    @Size(min = 1, max = 50, message = "Can add between 1 and 50 members at once")
    private List<String> userIds;
}
