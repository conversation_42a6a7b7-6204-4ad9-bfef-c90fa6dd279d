package com.bright.chat.dto.request;

import com.bright.chat.model.ConversationType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Request DTO for creating a conversation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateConversationRequest {

    @NotNull(message = "Conversation type is required")
    private ConversationType type;

    @Size(max = 100, message = "Conversation name cannot exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    @NotEmpty(message = "Participant IDs are required")
    @Size(min = 1, max = 500, message = "Conversation must have between 1 and 500 participants")
    private List<String> participantIds;

    private String avatarUrl;
}
