package com.bright.chat.dto.request;

import com.bright.chat.model.GroupPrivacy;
import com.bright.chat.model.GroupType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Request DTO for creating a group
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateGroupRequest {

    @NotBlank(message = "Group name is required")
    @Size(max = 100, message = "Group name cannot exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    @NotNull(message = "Group type is required")
    private GroupType type;

    @NotNull(message = "Group privacy is required")
    private GroupPrivacy privacy;

    @NotEmpty(message = "Member IDs are required")
    @Size(min = 1, max = 500, message = "Group must have between 1 and 500 members")
    private List<String> memberIds;

    private String avatarUrl;

    @Size(max = 1000, message = "Maximum members cannot exceed 1000")
    private Integer maxMembers;

    // Group permissions
    private Boolean membersCanSendMessages;
    private Boolean membersCanSendMedia;
    private Boolean membersCanAddOthers;
    private Boolean allowJoinByLink;
    private Boolean isDiscoverable;
}
