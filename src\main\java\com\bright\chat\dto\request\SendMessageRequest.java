package com.bright.chat.dto.request;

import com.bright.chat.model.MessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Request DTO for sending a message
 */
@Schema(description = "Request payload for sending a message")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendMessageRequest {

    @Schema(description = "ID of the conversation to send message to", example = "conv123")
    @NotBlank(message = "Conversation ID is required")
    private String conversationId;

    @Schema(description = "Message text content", example = "Hello, how are you?")
    @NotBlank(message = "Message text is required")
    @Size(max = 4000, message = "Message text cannot exceed 4000 characters")
    private String text;

    @Schema(description = "Type of message", example = "TEXT")
    @NotNull(message = "Message type is required")
    private MessageType type;

    @Schema(description = "ID of message being replied to (optional)", example = "msg456")
    private String replyToMessageId;

    @Schema(description = "List of user IDs mentioned in the message")
    private List<String> mentionedUserIds;

    @Schema(description = "List of file attachments")
    private List<AttachmentRequest> attachments;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentRequest {
        @NotBlank(message = "File name is required")
        private String fileName;
        
        @NotBlank(message = "File URL is required")
        private String fileUrl;
        
        @NotBlank(message = "MIME type is required")
        private String mimeType;
        
        private Long fileSize;
        private String thumbnailUrl;
        private Integer width;
        private Integer height;
        private Integer duration;
    }
}
