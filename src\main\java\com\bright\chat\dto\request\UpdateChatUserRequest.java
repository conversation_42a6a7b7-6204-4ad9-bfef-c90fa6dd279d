package com.bright.chat.dto.request;

import com.bright.chat.model.UserStatus;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO for updating chat user preferences
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateChatUserRequest {

    private String fcmToken;

    private UserStatus status;

    private Boolean allowNotifications;

    @Size(max = 50, message = "Timezone cannot exceed 50 characters")
    private String timezone;

    private Boolean isOnline;
}
