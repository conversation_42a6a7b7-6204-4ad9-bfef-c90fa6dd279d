package com.bright.chat.dto.request;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO for updating group information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateGroupRequest {

    @Size(max = 100, message = "Group name cannot exceed 100 characters")
    private String name;

    @Size(max = 500, message = "Description cannot exceed 500 characters")
    private String description;

    private String avatarUrl;

    @Size(max = 1000, message = "Maximum members cannot exceed 1000")
    private Integer maxMembers;

    // Group permissions
    private Boolean membersCanSendMessages;
    private Boolean membersCanSendMedia;
    private Boolean membersCanAddOthers;
    private Boolean allowJoinByLink;
    private Boolean isDiscoverable;
}
