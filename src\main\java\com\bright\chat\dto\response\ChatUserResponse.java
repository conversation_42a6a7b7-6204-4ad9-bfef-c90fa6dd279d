package com.bright.chat.dto.response;

import com.bright.chat.model.UserStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for chat user data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatUserResponse {

    private String userId;
    private String displayName;
    private String avatarUrl;
    private UserStatus status;
    private boolean isOnline;
    private LocalDateTime lastSeen;
    private boolean allowNotifications;
    private String timezone;
    private List<String> conversationIds;
    private List<String> groupIds;
    private LocalDateTime createdAt;
    private LocalDateTime lastSyncedAt;
}
