package com.bright.chat.dto.response;

import com.bright.chat.model.ConversationType;
import com.bright.chat.model.MessageType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for conversation data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationResponse {

    private String id;
    private String name;
    private String description;
    private String avatarUrl;
    private ConversationType type;
    private List<String> participantIds;
    private List<ParticipantResponse> participants;
    private boolean isActive;
    private long messageCount;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Last message info
    private String lastMessageId;
    private String lastMessageText;
    private String lastMessageSenderId;
    private String lastMessageSenderName;
    private LocalDateTime lastMessageTimestamp;
    private MessageType lastMessageType;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParticipantResponse {
        private String userId;
        private String displayName;
        private String avatarUrl;
        private boolean isOnline;
        private LocalDateTime lastSeen;
    }
}
