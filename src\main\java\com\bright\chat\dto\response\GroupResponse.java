package com.bright.chat.dto.response;

import com.bright.chat.model.GroupPrivacy;
import com.bright.chat.model.GroupType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for group data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupResponse {

    private String id;
    private String name;
    private String description;
    private String avatarUrl;
    private GroupType type;
    private GroupPrivacy privacy;
    private boolean isActive;
    private int memberCount;
    private int maxMembers;
    private String ownerId;
    private List<String> adminIds;
    private String conversationId;
    private LocalDateTime createdAt;
    private LocalDateTime lastActivity;
    private List<GroupMemberResponse> members;
    private GroupPermissionsResponse permissions;
    private String inviteCode;
    private LocalDateTime inviteCodeExpiry;
    private int maxInviteUses;
    private int currentInviteUses;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupMemberResponse {
        private String userId;
        private String displayName;
        private String avatarUrl;
        private String role;
        private String status;
        private LocalDateTime joinedAt;
        private boolean isOnline;
        private boolean isMuted;
        private LocalDateTime mutedUntil;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GroupPermissionsResponse {
        private boolean membersCanSendMessages;
        private boolean membersCanSendMedia;
        private boolean membersCanAddOthers;
        private boolean allowJoinByLink;
        private boolean isDiscoverable;
        private boolean allowForwarding;
        private boolean allowMentions;
    }
}
