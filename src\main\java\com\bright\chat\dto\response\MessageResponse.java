package com.bright.chat.dto.response;

import com.bright.chat.model.MessagePriority;
import com.bright.chat.model.MessageStatus;
import com.bright.chat.model.MessageType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Response DTO for message data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageResponse {

    private String id;
    private String conversationId;
    private String senderId;
    private String senderName;
    private String senderAvatarUrl;
    private String text;
    private MessageType type;
    private MessageStatus status;
    private MessagePriority priority;
    private LocalDateTime timestamp;
    private LocalDateTime editedAt;
    private boolean isEdited;
    private boolean isDeleted;
    private String replyToMessageId;
    private boolean isForwarded;
    private String originalMessageId;
    private List<AttachmentResponse> attachments;
    private Map<String, LocalDateTime> readBy;
    private Map<String, List<String>> reactions;
    private List<String> mentionedUserIds;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentResponse {
        private String id;
        private String fileName;
        private String mimeType;
        private Long fileSize;
        private String fileUrl;
        private String thumbnailUrl;
        private Integer width;
        private Integer height;
        private Integer duration;
    }
}
