package com.bright.chat.dto.response;

import com.bright.chat.model.NotificationPriority;
import com.bright.chat.model.NotificationStatus;
import com.bright.chat.model.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Response DTO for notification data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResponse {

    private String id;
    private String userId;
    private String senderId;
    private String title;
    private String body;
    private String imageUrl;
    private NotificationType type;
    private NotificationStatus status;
    private NotificationPriority priority;
    private String conversationId;
    private String messageId;
    private String groupId;
    private LocalDateTime createdAt;
    private LocalDateTime sentAt;
    private LocalDateTime deliveredAt;
    private LocalDateTime readAt;
    private Map<String, String> data;
}
