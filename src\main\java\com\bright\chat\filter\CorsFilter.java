package com.bright.chat.filter;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Custom CORS filter for handling specific CORS requirements
 * This filter runs before Spring's CORS handling for additional control
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class CorsFilter implements Filter {

    @Value("${cors.allowed-origins:http://localhost:3000,http://localhost:3001}")
    private String[] allowedOrigins;

    @Value("${cors.allowed-methods:GET,POST,PUT,DELETE,OPTIONS,PATCH}")
    private String[] allowedMethods;

    @Value("${cors.allowed-headers:*}")
    private String[] allowedHeaders;

    @Value("${cors.allow-credentials:true}")
    private boolean allowCredentials;

    @Value("${cors.max-age:3600}")
    private long maxAge;

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;

        String origin = request.getHeader("Origin");
        String method = request.getMethod();

        // Log CORS request for debugging
        if (origin != null) {
            log.debug("CORS request from origin: {} with method: {}", origin, method);
        }

        // Check if origin is allowed
        if (origin != null && isOriginAllowed(origin)) {
            // Set CORS headers
            response.setHeader("Access-Control-Allow-Origin", origin);
            response.setHeader("Access-Control-Allow-Methods", String.join(",", allowedMethods));
            
            if (allowedHeaders.length == 1 && "*".equals(allowedHeaders[0])) {
                response.setHeader("Access-Control-Allow-Headers", "*");
            } else {
                response.setHeader("Access-Control-Allow-Headers", String.join(",", allowedHeaders));
            }
            
            response.setHeader("Access-Control-Allow-Credentials", String.valueOf(allowCredentials));
            response.setHeader("Access-Control-Max-Age", String.valueOf(maxAge));
            
            // Expose custom headers
            response.setHeader("Access-Control-Expose-Headers", 
                    "X-Total-Count,X-Page-Count,X-User-Id,Authorization,Content-Disposition");
        }

        // Handle preflight requests
        if ("OPTIONS".equalsIgnoreCase(method)) {
            log.debug("Handling CORS preflight request from origin: {}", origin);
            response.setStatus(HttpServletResponse.SC_OK);
            return;
        }

        // Continue with the filter chain
        chain.doFilter(request, response);
    }

    /**
     * Check if the origin is allowed
     */
    private boolean isOriginAllowed(String origin) {
        if (origin == null) {
            return false;
        }

        List<String> allowedOriginsList = Arrays.asList(allowedOrigins);
        
        // Check for exact match
        if (allowedOriginsList.contains(origin)) {
            return true;
        }

        // Check for wildcard patterns
        for (String allowedOrigin : allowedOriginsList) {
            if (allowedOrigin.contains("*")) {
                String pattern = allowedOrigin.replace("*", ".*");
                if (origin.matches(pattern)) {
                    return true;
                }
            }
        }

        // Log rejected origins for security monitoring
        log.warn("CORS request rejected from unauthorized origin: {}", origin);
        return false;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("CORS Filter initialized with allowed origins: {}", Arrays.toString(allowedOrigins));
    }

    @Override
    public void destroy() {
        log.info("CORS Filter destroyed");
    }
}
