package com.bright.chat.mapper;

import com.bright.chat.dto.response.ChatUserResponse;
import com.bright.chat.model.ChatUser;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for ChatUser entities and DTOs
 */
@Component
public class ChatUserMapper {

    public ChatUserResponse toResponse(ChatUser chatUser) {
        if (chatUser == null) {
            return null;
        }

        return ChatUserResponse.builder()
                .userId(chatUser.getUserId())
                .displayName(chatUser.getDisplayName())
                .avatarUrl(chatUser.getAvatarUrl())
                .status(chatUser.getStatus())
                .isOnline(chatUser.isOnline())
                .lastSeen(chatUser.getLastSeen())
                .allowNotifications(chatUser.isAllowNotifications())
                .timezone(chatUser.getTimezone())
                .conversationIds(chatUser.getConversationIds())
                .groupIds(chatUser.getGroupIds())
                .createdAt(chatUser.getCreatedAt())
                .lastSyncedAt(chatUser.getLastSyncedAt())
                .build();
    }

    public List<ChatUserResponse> toResponses(List<ChatUser> chatUsers) {
        if (chatUsers == null) {
            return null;
        }
        return chatUsers.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
