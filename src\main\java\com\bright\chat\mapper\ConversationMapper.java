package com.bright.chat.mapper;

import com.bright.chat.dto.response.ConversationResponse;
import com.bright.chat.model.ChatUser;
import com.bright.chat.model.Conversation;
import com.bright.chat.service.ChatUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for Conversation entities and DTOs
 */
@Component
@RequiredArgsConstructor
public class ConversationMapper {

    private final ChatUserService chatUserService;

    public ConversationResponse toResponse(Conversation conversation) {
        if (conversation == null) {
            return null;
        }

        return ConversationResponse.builder()
                .id(conversation.getId())
                .name(conversation.getName())
                .description(conversation.getDescription())
                .avatarUrl(conversation.getAvatarUrl())
                .type(conversation.getType())
                .participantIds(conversation.getParticipantIds())
                .participants(toParticipantResponses(conversation.getParticipantIds()))
                .isActive(conversation.isActive())
                .messageCount(conversation.getMessageCount())
                .createdAt(conversation.getCreatedAt())
                .updatedAt(conversation.getUpdatedAt())
                .lastMessageId(conversation.getLastMessageId())
                .lastMessageText(conversation.getLastMessageText())
                .lastMessageSenderId(conversation.getLastMessageSenderId())
                .lastMessageTimestamp(conversation.getLastMessageTimestamp())
                .lastMessageType(conversation.getLastMessageType())
                .build();
    }

    public List<ConversationResponse> toResponses(List<Conversation> conversations) {
        if (conversations == null) {
            return null;
        }
        return conversations.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    private List<ConversationResponse.ParticipantResponse> toParticipantResponses(List<String> participantIds) {
        if (participantIds == null) {
            return null;
        }

        List<ChatUser> chatUsers = chatUserService.getChatUsers(participantIds);
        return chatUsers.stream()
                .map(this::toParticipantResponse)
                .collect(Collectors.toList());
    }

    private ConversationResponse.ParticipantResponse toParticipantResponse(ChatUser chatUser) {
        if (chatUser == null) {
            return null;
        }

        return ConversationResponse.ParticipantResponse.builder()
                .userId(chatUser.getUserId())
                .displayName(chatUser.getDisplayName())
                .avatarUrl(chatUser.getAvatarUrl())
                .isOnline(chatUser.isOnline())
                .lastSeen(chatUser.getLastSeen())
                .build();
    }
}
