package com.bright.chat.mapper;

import com.bright.chat.dto.response.GroupResponse;
import com.bright.chat.model.ChatUser;
import com.bright.chat.model.Group;
import com.bright.chat.model.GroupMember;
import com.bright.chat.service.ChatUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for Group entities and DTOs
 */
@Component
@RequiredArgsConstructor
public class GroupMapper {

    private final ChatUserService chatUserService;

    public GroupResponse toResponse(Group group) {
        if (group == null) {
            return null;
        }

        return GroupResponse.builder()
                .id(group.getId())
                .name(group.getName())
                .description(group.getDescription())
                .avatarUrl(group.getAvatarUrl())
                .type(group.getType())
                .privacy(group.getPrivacy())
                .isActive(group.isActive())
                .memberCount(group.getMemberCount())
                .maxMembers(group.getMaxMembers())
                .ownerId(group.getOwnerId())
                .adminIds(group.getAdminIds())
                .conversationId(group.getConversationId())
                .createdAt(group.getCreatedAt())
                .lastActivity(group.getLastActivity())
                .members(toMemberResponses(group))
                .permissions(toPermissionsResponse(group))
                .inviteCode(group.getInviteCode())
                .inviteCodeExpiry(group.getInviteCodeExpiry())
                .maxInviteUses(group.getMaxInviteUses())
                .currentInviteUses(group.getCurrentInviteUses())
                .build();
    }

    public List<GroupResponse> toResponses(List<Group> groups) {
        if (groups == null) {
            return null;
        }
        return groups.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    private List<GroupResponse.GroupMemberResponse> toMemberResponses(Group group) {
        if (group.getMembers() == null) {
            return null;
        }

        return group.getMembers().entrySet().stream()
                .map(entry -> {
                    String userId = entry.getKey();
                    GroupMember member = entry.getValue();
                    ChatUser chatUser = chatUserService.getChatUser(userId).orElse(null);
                    
                    return GroupResponse.GroupMemberResponse.builder()
                            .userId(userId)
                            .displayName(chatUser != null ? chatUser.getDisplayName() : member.getDisplayName())
                            .avatarUrl(chatUser != null ? chatUser.getAvatarUrl() : member.getAvatarUrl())
                            .role(member.getRole().toString())
                            .status(member.getStatus().toString())
                            .joinedAt(member.getJoinedAt())
                            .isOnline(chatUser != null && chatUser.isOnline())
                            .isMuted(member.isMuted())
                            .mutedUntil(member.getMutedUntil())
                            .build();
                })
                .collect(Collectors.toList());
    }

    private GroupResponse.GroupPermissionsResponse toPermissionsResponse(Group group) {
        if (group.getPermissions() == null) {
            return null;
        }

        return GroupResponse.GroupPermissionsResponse.builder()
                .membersCanSendMessages(group.getPermissions().isMembersCanSendMessages())
                .membersCanSendMedia(group.getPermissions().isMembersCanSendMedia())
                .membersCanAddOthers(group.getPermissions().isMembersCanAddOthers())
                .allowJoinByLink(group.getPermissions().isAllowJoinByLink())
                .isDiscoverable(group.getPermissions().isDiscoverable())
                .allowForwarding(group.getPermissions().isAllowForwarding())
                .allowMentions(group.getPermissions().isAllowMentions())
                .build();
    }
}
