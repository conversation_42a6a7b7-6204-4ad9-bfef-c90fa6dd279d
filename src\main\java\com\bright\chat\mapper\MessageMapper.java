package com.bright.chat.mapper;

import com.bright.chat.dto.request.SendMessageRequest;
import com.bright.chat.dto.response.MessageResponse;
import com.bright.chat.model.Message;
import com.bright.chat.model.MessageAttachment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for Message entities and DTOs
 */
@Component
public class MessageMapper {

    public MessageResponse toResponse(Message message) {
        if (message == null) {
            return null;
        }

        return MessageResponse.builder()
                .id(message.getId())
                .conversationId(message.getConversationId())
                .senderId(message.getSenderId())
                .senderName(message.getSenderName())
                .senderAvatarUrl(message.getSenderAvatarUrl())
                .text(message.getText())
                .type(message.getType())
                .status(message.getStatus())
                .priority(message.getPriority())
                .timestamp(message.getTimestamp())
                .editedAt(message.getEditedAt())
                .isEdited(message.isEdited())
                .isDeleted(message.isDeleted())
                .replyToMessageId(message.getReplyToMessageId())
                .isForwarded(message.isForwarded())
                .originalMessageId(message.getOriginalMessageId())
                .attachments(toAttachmentResponses(message.getAttachments()))
                .readBy(message.getReadBy())
                .reactions(message.getReactions())
                .mentionedUserIds(message.getMentionedUserIds())
                .build();
    }

    public List<MessageResponse> toResponses(List<Message> messages) {
        if (messages == null) {
            return null;
        }
        return messages.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }

    public List<MessageAttachment> toAttachments(List<SendMessageRequest.AttachmentRequest> attachmentRequests) {
        if (attachmentRequests == null) {
            return null;
        }
        return attachmentRequests.stream()
                .map(this::toAttachment)
                .collect(Collectors.toList());
    }

    private MessageAttachment toAttachment(SendMessageRequest.AttachmentRequest request) {
        if (request == null) {
            return null;
        }

        return MessageAttachment.builder()
                .fileName(request.getFileName())
                .originalFileName(request.getFileName())
                .mimeType(request.getMimeType())
                .fileSize(request.getFileSize())
                .fileUrl(request.getFileUrl())
                .thumbnailUrl(request.getThumbnailUrl())
                .width(request.getWidth())
                .height(request.getHeight())
                .duration(request.getDuration())
                .build();
    }

    private List<MessageResponse.AttachmentResponse> toAttachmentResponses(List<MessageAttachment> attachments) {
        if (attachments == null) {
            return null;
        }
        return attachments.stream()
                .map(this::toAttachmentResponse)
                .collect(Collectors.toList());
    }

    private MessageResponse.AttachmentResponse toAttachmentResponse(MessageAttachment attachment) {
        if (attachment == null) {
            return null;
        }

        return MessageResponse.AttachmentResponse.builder()
                .id(attachment.getId())
                .fileName(attachment.getFileName())
                .mimeType(attachment.getMimeType())
                .fileSize(attachment.getFileSize())
                .fileUrl(attachment.getFileUrl())
                .thumbnailUrl(attachment.getThumbnailUrl())
                .width(attachment.getWidth())
                .height(attachment.getHeight())
                .duration(attachment.getDuration())
                .build();
    }
}
