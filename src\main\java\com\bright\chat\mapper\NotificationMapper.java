package com.bright.chat.mapper;

import com.bright.chat.dto.response.NotificationResponse;
import com.bright.chat.model.Notification;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for Notification entities and DTOs
 */
@Component
public class NotificationMapper {

    public NotificationResponse toResponse(Notification notification) {
        if (notification == null) {
            return null;
        }

        return NotificationResponse.builder()
                .id(notification.getId())
                .userId(notification.getUserId())
                .senderId(notification.getSenderId())
                .title(notification.getTitle())
                .body(notification.getBody())
                .imageUrl(notification.getImageUrl())
                .type(notification.getType())
                .status(notification.getStatus())
                .priority(notification.getPriority())
                .conversationId(notification.getConversationId())
                .messageId(notification.getMessageId())
                .groupId(notification.getGroupId())
                .createdAt(notification.getCreatedAt())
                .sentAt(notification.getSentAt())
                .deliveredAt(notification.getDeliveredAt())
                .readAt(notification.getReadAt())
                .data(notification.getData())
                .build();
    }

    public List<NotificationResponse> toResponses(List<Notification> notifications) {
        if (notifications == null) {
            return null;
        }
        return notifications.stream()
                .map(this::toResponse)
                .collect(Collectors.toList());
    }
}
