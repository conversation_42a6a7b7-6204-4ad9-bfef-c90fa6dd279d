package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Chat User Profile - Minimal user data for chat functionality
 * This is NOT the main user entity (which is managed by User Service)
 * Stored in Firestore collection: chat_users
 * Contains only chat-specific user data and cached display info
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatUser {

    private String userId; // Reference to user ID from User Service

    // Cached display information (synced from User Service)
    private String displayName;
    private String avatarUrl;

    // Chat-specific data
    private String fcmToken; // FCM token for push notifications
    private UserStatus status; // Online/offline status
    private LocalDateTime lastSeen;

    // Chat preferences
    private boolean isOnline;
    private boolean allowNotifications;
    private String timezone;

    // Chat relationships
    private List<String> conversationIds; // Conversations user is part of
    private List<String> groupIds; // Groups user is member of
    private List<String> blockedUserIds; // Blocked users in chat

    // Metadata
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastSyncedAt; // Last time synced with User Service
}
