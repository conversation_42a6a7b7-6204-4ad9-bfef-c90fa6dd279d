package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Conversation entity for chat system
 * Stored in Firestore collection: conversations
 * Supports both direct messages (2 participants) and group chats (multiple participants)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Conversation {
    
    private String id; // Document ID in Firestore
    private String name; // Optional name for group conversations
    private String description; // Optional description for group conversations
    private String avatarUrl; // Optional avatar for group conversations
    
    // Conversation type
    private ConversationType type;
    
    // Participants
    private List<String> participantIds;
    private Map<String, ConversationMember> members; // userId -> member details
    
    // Last message info for quick display
    private String lastMessageId;
    private String lastMessageText;
    private String lastMessageSenderId;
    private LocalDateTime lastMessageTimestamp;
    private MessageType lastMessageType;
    
    // Conversation settings
    private boolean isActive;
    private boolean allowNewMembers; // For groups
    private boolean isPublic; // For groups - can be discovered and joined
    
    // Admin settings for groups
    private List<String> adminIds;
    private String createdBy;
    
    // Metadata
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Message count for pagination
    private long messageCount;
    
    // Conversation settings per user
    private Map<String, ConversationSettings> userSettings; // userId -> settings
}
