package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Represents a member in a conversation with their role and metadata
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationMember {
    
    private String userId;
    private String displayName;
    private String avatarUrl;
    
    // Member role in conversation
    private MemberRole role;
    
    // Join/leave timestamps
    private LocalDateTime joinedAt;
    private LocalDateTime leftAt;
    
    // Member status in conversation
    private boolean isActive;
    private boolean isMuted;
    
    // Last read message for this member
    private String lastReadMessageId;
    private LocalDateTime lastReadTimestamp;
    
    // Permissions
    private boolean canSendMessages;
    private boolean canAddMembers;
    private boolean canRemoveMembers;
    private boolean canEditConversation;
}
