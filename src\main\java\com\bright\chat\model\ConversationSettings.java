package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * User-specific settings for a conversation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationSettings {
    
    // Notification settings
    private boolean notificationsEnabled;
    private boolean soundEnabled;
    private boolean vibrationEnabled;
    
    // Display settings
    private boolean isPinned;
    private boolean isArchived;
    private boolean isHidden;
    
    // Custom settings
    private String customName; // User can set custom name for conversation
    private String customAvatarUrl; // User can set custom avatar
    
    // Privacy settings
    private boolean showReadReceipts;
    private boolean showTypingIndicator;
}
