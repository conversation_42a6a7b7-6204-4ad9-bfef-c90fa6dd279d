package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Group entity for chat system
 * Stored in Firestore collection: groups
 * Groups are special types of conversations with additional management features
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Group {
    
    private String id; // Document ID in Firestore
    private String name;
    private String description;
    private String avatarUrl;
    private String bannerUrl;
    
    // Group settings
    private GroupType type;
    private GroupPrivacy privacy;
    private boolean isActive;
    
    // Member management
    private List<String> memberIds;
    private Map<String, GroupMember> members; // userId -> member details
    private List<String> adminIds;
    private String ownerId;
    private int maxMembers;
    
    // Group permissions
    private GroupPermissions permissions;
    
    // Invitation settings
    private boolean allowInviteLinks;
    private String inviteCode;
    private LocalDateTime inviteCodeExpiry;
    private int maxInviteUses;
    private int currentInviteUses;
    
    // Group statistics
    private int memberCount;
    private int messageCount;
    private LocalDateTime lastActivity;
    
    // Moderation
    private List<String> bannedUserIds;
    private List<String> mutedUserIds;
    private Map<String, LocalDateTime> muteExpiry; // userId -> expiry time
    
    // Associated conversation
    private String conversationId;
    
    // Metadata
    private String createdBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Group categories/tags
    private List<String> tags;
    private String category;
    
    // Location (for location-based groups)
    private GroupLocation location;
}
