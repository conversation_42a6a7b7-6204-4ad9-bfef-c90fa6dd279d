package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Location information for location-based groups
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupLocation {
    
    private double latitude;
    private double longitude;
    private String address;
    private String city;
    private String country;
    private String postalCode;
    
    // Radius for location-based groups (in meters)
    private double radius;
    
    // Location name/description
    private String name;
    private String description;
}
