package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Represents a member in a group with their role and metadata
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupMember {
    
    private String userId;
    private String displayName;
    private String avatarUrl;
    
    // Member role in group
    private GroupRole role;
    
    // Join/leave timestamps
    private LocalDateTime joinedAt;
    private LocalDateTime leftAt;
    private String invitedBy;
    
    // Member status
    private GroupMemberStatus status;
    private boolean isActive;
    
    // Permissions
    private boolean canSendMessages;
    private boolean canAddMembers;
    private boolean canRemoveMembers;
    private boolean canEditGroup;
    private boolean canDeleteMessages;
    private boolean canPinMessages;
    
    // Moderation
    private boolean isMuted;
    private LocalDateTime mutedUntil;
    private String mutedBy;
    private String muteReason;
    
    // Custom title/nickname in group
    private String customTitle;
    private String nickname;
}
