package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Group-level permissions configuration
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupPermissions {
    
    // Message permissions
    private boolean membersCanSendMessages;
    private boolean membersCanSendMedia;
    private boolean membersCanSendPolls;
    private boolean membersCanSendStickers;
    
    // Member management permissions
    private boolean membersCanAddOthers;
    private boolean membersCanRemoveOthers;
    private boolean membersCanChangeGroupInfo;
    
    // Message management permissions
    private boolean membersCanDeleteMessages;
    private boolean membersCanPinMessages;
    private boolean membersCanEditMessages;
    
    // Group discovery permissions
    private boolean isDiscoverable;
    private boolean allowJoinByLink;
    private boolean requireApprovalToJoin;
    
    // Content permissions
    private boolean allowForwarding;
    private boolean allowScreenshots;
    private boolean allowDownloads;
    
    // Notification permissions
    private boolean allowMentions;
    private boolean allowChannelMentions;
}
