package com.bright.chat.model;

/**
 * Enum for group member roles
 */
public enum GroupRole {
    OWNER,          // Creator of the group, has all permissions
    ADMIN,          // Can manage members and group settings
    MODERATOR,      // Can moderate messages and manage some settings
    MEMBER,         // Regular member with basic permissions
    RESTRICTED      // Member with limited permissions
}
