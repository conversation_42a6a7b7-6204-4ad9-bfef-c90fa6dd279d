package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Message entity for chat system
 * Stored in Firestore subcollection: conversations/{conversationId}/messages
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Message {
    
    private String id; // Document ID in Firestore
    private String conversationId;
    private String senderId;
    private String senderName;
    private String senderAvatarUrl;
    
    // Message content
    private String text;
    private MessageType type;
    private String content; // JSON string for complex content (files, locations, etc.)
    
    // File attachments
    private List<MessageAttachment> attachments;
    
    // Message metadata
    private LocalDateTime timestamp;
    private LocalDateTime editedAt;
    private boolean isEdited;
    private boolean isDeleted;
    private String deletedBy;
    private LocalDateTime deletedAt;
    
    // Reply/thread functionality
    private String replyToMessageId;
    private String threadId; // For threaded conversations
    
    // Message status
    private MessageStatus status;
    
    // Read receipts - userId -> timestamp when read
    private Map<String, LocalDateTime> readBy;
    
    // Reactions - emoji -> list of user IDs
    private Map<String, List<String>> reactions;
    
    // Mentions
    private List<String> mentionedUserIds;
    
    // Message priority/importance
    private MessagePriority priority;
    
    // Forwarded message info
    private boolean isForwarded;
    private String originalMessageId;
    private String originalConversationId;
    
    // System message info (for join/leave notifications, etc.)
    private boolean isSystemMessage;
    private String systemMessageType;
    private Map<String, Object> systemMessageData;
}
