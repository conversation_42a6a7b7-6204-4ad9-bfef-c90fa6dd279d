package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a file attachment in a message
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageAttachment {
    
    private String id;
    private String fileName;
    private String originalFileName;
    private String mimeType;
    private long fileSize; // in bytes
    
    // File URLs
    private String fileUrl; // Full resolution file URL
    private String thumbnailUrl; // Thumbnail URL for images/videos
    private String previewUrl; // Preview URL for documents
    
    // File metadata
    private Integer width; // For images/videos
    private Integer height; // For images/videos
    private Integer duration; // For audio/video in seconds
    
    // Upload status
    private AttachmentStatus status;
    private String uploadId; // For tracking upload progress
    
    // File storage info
    private String storageProvider; // Firebase Storage, AWS S3, etc.
    private String storagePath;
    private String storageReference;
}
