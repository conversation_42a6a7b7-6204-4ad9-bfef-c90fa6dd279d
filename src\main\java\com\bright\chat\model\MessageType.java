package com.bright.chat.model;

/**
 * Enum for message types
 */
public enum MessageType {
    TEXT,           // Plain text message
    IMAGE,          // Image attachment
    VIDEO,          // Video attachment
    AUDIO,          // Audio/voice message
    FILE,           // File attachment
    LOCATION,       // Location sharing
    CONTACT,        // Contact sharing
    STICKER,        // Sticker/emoji
    GIF,            // GIF image
    VOICE_NOTE,     // Voice note recording
    SYSTEM,         // System generated message
    POLL,           // Poll message
    EVENT,          // Event/calendar invitation
    LINK_PREVIEW    // Message with link preview
}
