package com.bright.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Notification entity for FCM push notifications
 * Stored in Firestore collection: notifications
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Notification {
    
    private String id; // Document ID in Firestore
    private String userId; // Recipient user ID
    private String senderId; // Sender user ID (optional)
    
    // Notification content
    private String title;
    private String body;
    private String imageUrl;
    
    // Notification type and data
    private NotificationType type;
    private Map<String, String> data; // Additional data for the notification
    
    // Related entities
    private String conversationId;
    private String messageId;
    private String groupId;
    
    // Notification status
    private NotificationStatus status;
    private LocalDateTime sentAt;
    private LocalDateTime deliveredAt;
    private LocalDateTime readAt;
    
    // FCM specific
    private String fcmMessageId;
    private String fcmToken;
    
    // Retry mechanism
    private int retryCount;
    private LocalDateTime nextRetryAt;
    private String failureReason;
    
    // Metadata
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Notification settings
    private NotificationPriority priority;
    private boolean isSilent;
    private String sound;
    private String clickAction;
}
