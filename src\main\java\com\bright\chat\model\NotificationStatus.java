package com.bright.chat.model;

/**
 * Enum for notification delivery status
 */
public enum NotificationStatus {
    PENDING,        // Notification created but not sent
    SENT,           // Notification sent to FCM
    DELIVERED,      // Notification delivered to device
    READ,           // Notification read by user
    FAILED,         // Notification failed to send
    EXPIRED         // Notification expired
}
