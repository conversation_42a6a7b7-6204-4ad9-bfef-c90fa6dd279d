package com.bright.chat.model;

/**
 * Enum for notification types
 */
public enum NotificationType {
    NEW_MESSAGE,            // New message received
    NEW_GROUP_MESSAGE,      // New message in group
    MENTION,                // User mentioned in message
    GROUP_INVITE,           // Invited to group
    <PERSON>ROUP_JOIN,             // Someone joined group
    GROUP_LEAVE,            // Someone left group
    GROUP_ADMIN,            // Promoted to group admin
    FRIEND_REQUEST,         // Friend request received
    FRIEND_ACCEPTED,        // Friend request accepted
    CALL_INCOMING,          // Incoming call
    CALL_MISSED,            // Missed call
    MESSAGE_REACTION,       // Someone reacted to message
    SYSTEM                  // System notification
}
