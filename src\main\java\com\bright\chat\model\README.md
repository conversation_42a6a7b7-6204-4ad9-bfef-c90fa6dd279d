# Chat System Data Models

## Microservice Architecture

### User Management
- **User Service**: Manages complete user profiles, authentication, etc.
- **Chat Service**: Only stores chat-specific user data and cached display info
- **Communication**: Chat Service calls User Service APIs for user information

## Firestore Database Structure

### Collections

#### 1. Chat Users Collection (`chat_users`)
- **Document ID**: User ID (same as User Service)
- **Model**: `ChatUser.java`
- **Purpose**: Store chat-specific user data, FCM tokens, and cached display info
- **Note**: This is NOT the main user entity (managed by User Service)

#### 2. Conversations Collection (`conversations`)
- **Document ID**: Conversation ID
- **Model**: `Conversation.java`
- **Purpose**: Store conversation metadata for both direct messages and group chats
- **Subcollections**:
  - `messages`: Individual messages in the conversation

#### 3. Messages Subcollection (`conversations/{conversationId}/messages`)
- **Document ID**: Message ID
- **Model**: `Message.java`
- **Purpose**: Store individual messages with content, attachments, and metadata

#### 4. Groups Collection (`groups`)
- **Document ID**: Group ID
- **Model**: `Group.java`
- **Purpose**: Store group-specific information and management data
- **Relationship**: Each group has a corresponding conversation

#### 5. Notifications Collection (`notifications`)
- **Document ID**: Notification ID
- **Model**: `Notification.java`
- **Purpose**: Store FCM notification history and status

## Key Design Decisions

### 1. Microservice Separation
- **User Service**: Manages user accounts, profiles, authentication
- **Chat Service**: Manages conversations, messages, groups, notifications
- **Data Sync**: Chat service caches minimal user display data
- **Communication**: REST API calls between services

### 2. Conversation vs Group Separation
- **Conversations**: Handle the messaging functionality for both direct and group chats
- **Groups**: Handle group-specific features like member management, permissions, etc.
- **Relationship**: Groups reference their conversation via `conversationId`

### 2. Message Storage
- Messages are stored as subcollections under conversations for better scalability
- Supports pagination and real-time updates
- Includes rich metadata for features like reactions, replies, and read receipts

### 3. User Management
- Users maintain lists of conversation and group IDs for quick access
- FCM tokens stored with user data for push notifications
- Support for user status and presence

### 4. Permissions System
- Role-based permissions at both conversation and group levels
- Granular permissions for different actions
- Support for custom permissions per user

### 5. Notification System
- Separate notification entities for tracking delivery status
- Support for different notification types and priorities
- Retry mechanism for failed notifications

## Usage Examples

### Syncing User Data from User Service
```java
// Get user info from User Service
UserInfo userInfo = userServiceClient.getUserById("user123");

// Create or update ChatUser
ChatUser chatUser = ChatUser.builder()
    .userId(userInfo.getId())
    .displayName(userInfo.getDisplayName())
    .avatarUrl(userInfo.getAvatarUrl())
    .lastSyncedAt(LocalDateTime.now())
    .build();
```

### Creating a Direct Conversation
```java
// Validate users exist in User Service first
List<String> userIds = Arrays.asList("user1", "user2");
List<String> validUserIds = userServiceClient.validateUserIds(userIds);

if (validUserIds.size() == userIds.size()) {
    Conversation conversation = Conversation.builder()
        .type(ConversationType.DIRECT)
        .participantIds(validUserIds)
        .isActive(true)
        .createdAt(LocalDateTime.now())
        .build();
}
```

### Creating a Group Chat
```java
// Create group
Group group = Group.builder()
    .name("My Group")
    .type(GroupType.PRIVATE)
    .privacy(GroupPrivacy.PRIVATE)
    .ownerId("user1")
    .createdAt(LocalDateTime.now())
    .build();

// Create associated conversation
Conversation conversation = Conversation.builder()
    .type(ConversationType.GROUP)
    .name("My Group")
    .participantIds(Arrays.asList("user1", "user2", "user3"))
    .isActive(true)
    .createdAt(LocalDateTime.now())
    .build();
```

### Sending a Message
```java
Message message = Message.builder()
    .conversationId("conv123")
    .senderId("user1")
    .text("Hello, world!")
    .type(MessageType.TEXT)
    .timestamp(LocalDateTime.now())
    .status(MessageStatus.SENT)
    .build();
```

## Indexing Recommendations

### Firestore Indexes
1. **Messages**: `conversationId` + `timestamp` (descending)
2. **Conversations**: `participantIds` + `lastMessageTimestamp` (descending)
3. **Groups**: `memberIds` + `lastActivity` (descending)
4. **Notifications**: `userId` + `createdAt` (descending)
5. **Users**: `email` (for user lookup)

## Security Rules Considerations

1. Users can only read/write their own user document
2. Users can only access conversations they are participants in
3. Users can only access messages in conversations they belong to
4. Group access controlled by membership
5. Notifications only accessible by the target user
