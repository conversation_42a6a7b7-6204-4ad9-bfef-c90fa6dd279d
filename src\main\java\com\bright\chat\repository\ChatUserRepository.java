package com.bright.chat.repository;

import com.bright.chat.model.ChatUser;
import com.bright.chat.service.FirestoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Repository for ChatUser operations in Firestore
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ChatUserRepository {

    private static final String COLLECTION_NAME = "chat_users";
    
    private final FirestoreService firestoreService;

    /**
     * Save or update a chat user
     */
    public void save(ChatUser chatUser) {
        chatUser.setUpdatedAt(LocalDateTime.now());
        if (chatUser.getCreatedAt() == null) {
            chatUser.setCreatedAt(LocalDateTime.now());
        }
        firestoreService.saveDocument(COLLECTION_NAME, chatUser.getUserId(), chatUser);
        log.info("Saved chat user: {}", chatUser.getUserId());
    }

    /**
     * Find chat user by user ID
     */
    public Optional<ChatUser> findByUserId(String userId) {
        ChatUser chatUser = firestoreService.getDocument(COLLECTION_NAME, userId, ChatUser.class);
        return Optional.ofNullable(chatUser);
    }

    /**
     * Find multiple chat users by user IDs
     */
    public List<ChatUser> findByUserIds(List<String> userIds) {
        return userIds.stream()
                .map(this::findByUserId)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();
    }

    /**
     * Update FCM token for a user
     */
    public void updateFcmToken(String userId, String fcmToken) {
        Map<String, Object> updates = Map.of(
                "fcmToken", fcmToken,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, userId, updates);
        log.info("Updated FCM token for user: {}", userId);
    }

    /**
     * Update user status (online/offline)
     */
    public void updateUserStatus(String userId, boolean isOnline) {
        Map<String, Object> updates = Map.of(
                "isOnline", isOnline,
                "lastSeen", LocalDateTime.now(),
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, userId, updates);
        log.info("Updated status for user {}: {}", userId, isOnline ? "online" : "offline");
    }

    /**
     * Add conversation to user's conversation list
     */
    public void addConversation(String userId, String conversationId) {
        ChatUser chatUser = findByUserId(userId).orElse(null);
        if (chatUser != null) {
            if (chatUser.getConversationIds() == null) {
                chatUser.setConversationIds(List.of(conversationId));
            } else if (!chatUser.getConversationIds().contains(conversationId)) {
                chatUser.getConversationIds().add(conversationId);
            }
            save(chatUser);
            log.info("Added conversation {} to user {}", conversationId, userId);
        }
    }

    /**
     * Remove conversation from user's conversation list
     */
    public void removeConversation(String userId, String conversationId) {
        ChatUser chatUser = findByUserId(userId).orElse(null);
        if (chatUser != null && chatUser.getConversationIds() != null) {
            chatUser.getConversationIds().remove(conversationId);
            save(chatUser);
            log.info("Removed conversation {} from user {}", conversationId, userId);
        }
    }

    /**
     * Add group to user's group list
     */
    public void addGroup(String userId, String groupId) {
        ChatUser chatUser = findByUserId(userId).orElse(null);
        if (chatUser != null) {
            if (chatUser.getGroupIds() == null) {
                chatUser.setGroupIds(List.of(groupId));
            } else if (!chatUser.getGroupIds().contains(groupId)) {
                chatUser.getGroupIds().add(groupId);
            }
            save(chatUser);
            log.info("Added group {} to user {}", groupId, userId);
        }
    }

    /**
     * Remove group from user's group list
     */
    public void removeGroup(String userId, String groupId) {
        ChatUser chatUser = findByUserId(userId).orElse(null);
        if (chatUser != null && chatUser.getGroupIds() != null) {
            chatUser.getGroupIds().remove(groupId);
            save(chatUser);
            log.info("Removed group {} from user {}", groupId, userId);
        }
    }

    /**
     * Block a user
     */
    public void blockUser(String userId, String blockedUserId) {
        ChatUser chatUser = findByUserId(userId).orElse(null);
        if (chatUser != null) {
            if (chatUser.getBlockedUserIds() == null) {
                chatUser.setBlockedUserIds(List.of(blockedUserId));
            } else if (!chatUser.getBlockedUserIds().contains(blockedUserId)) {
                chatUser.getBlockedUserIds().add(blockedUserId);
            }
            save(chatUser);
            log.info("User {} blocked user {}", userId, blockedUserId);
        }
    }

    /**
     * Unblock a user
     */
    public void unblockUser(String userId, String blockedUserId) {
        ChatUser chatUser = findByUserId(userId).orElse(null);
        if (chatUser != null && chatUser.getBlockedUserIds() != null) {
            chatUser.getBlockedUserIds().remove(blockedUserId);
            save(chatUser);
            log.info("User {} unblocked user {}", userId, blockedUserId);
        }
    }

    /**
     * Check if user is blocked by another user
     */
    public boolean isUserBlocked(String userId, String potentialBlockedUserId) {
        ChatUser chatUser = findByUserId(userId).orElse(null);
        return chatUser != null && 
               chatUser.getBlockedUserIds() != null && 
               chatUser.getBlockedUserIds().contains(potentialBlockedUserId);
    }

    /**
     * Update user display information (synced from User Service)
     */
    public void updateDisplayInfo(String userId, String displayName, String avatarUrl) {
        Map<String, Object> updates = Map.of(
                "displayName", displayName,
                "avatarUrl", avatarUrl,
                "lastSyncedAt", LocalDateTime.now(),
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, userId, updates);
        log.info("Updated display info for user: {}", userId);
    }

    /**
     * Delete chat user
     */
    public void delete(String userId) {
        firestoreService.deleteDocument(COLLECTION_NAME, userId);
        log.info("Deleted chat user: {}", userId);
    }
}
