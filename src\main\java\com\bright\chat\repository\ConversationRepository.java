package com.bright.chat.repository;

import com.bright.chat.model.Conversation;
import com.bright.chat.model.ConversationType;
import com.bright.chat.service.FirestoreService;
import com.google.cloud.firestore.Query;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Repository for Conversation operations in Firestore
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ConversationRepository {

    private static final String COLLECTION_NAME = "conversations";
    
    private final FirestoreService firestoreService;

    /**
     * Save a new conversation
     */
    public String save(Conversation conversation) {
        conversation.setUpdatedAt(LocalDateTime.now());
        if (conversation.getCreatedAt() == null) {
            conversation.setCreatedAt(LocalDateTime.now());
        }
        
        if (conversation.getId() != null) {
            firestoreService.saveDocument(COLLECTION_NAME, conversation.getId(), conversation);
            log.info("Updated conversation: {}", conversation.getId());
            return conversation.getId();
        } else {
            String id = firestoreService.saveDocument(COLLECTION_NAME, conversation);
            log.info("Created new conversation: {}", id);
            return id;
        }
    }

    /**
     * Find conversation by ID
     */
    public Optional<Conversation> findById(String conversationId) {
        Conversation conversation = firestoreService.getDocument(COLLECTION_NAME, conversationId, Conversation.class);
        return Optional.ofNullable(conversation);
    }

    /**
     * Find conversations by participant ID
     */
    public List<Conversation> findByParticipantId(String userId) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "participantIds", userId, Conversation.class);
    }

    /**
     * Find direct conversation between two users
     */
    public Optional<Conversation> findDirectConversation(String user1Id, String user2Id) {
        List<Conversation> conversations = findByParticipantId(user1Id);
        
        return conversations.stream()
                .filter(conv -> conv.getType() == ConversationType.DIRECT)
                .filter(conv -> conv.getParticipantIds().contains(user2Id))
                .filter(conv -> conv.getParticipantIds().size() == 2)
                .findFirst();
    }

    /**
     * Update last message information
     */
    public void updateLastMessage(String conversationId, String messageId, String messageText, 
                                 String senderId, LocalDateTime timestamp) {
        Map<String, Object> updates = Map.of(
                "lastMessageId", messageId,
                "lastMessageText", messageText,
                "lastMessageSenderId", senderId,
                "lastMessageTimestamp", timestamp,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, conversationId, updates);
        log.info("Updated last message for conversation: {}", conversationId);
    }

    /**
     * Add participant to conversation
     */
    public void addParticipant(String conversationId, String userId) {
        Conversation conversation = findById(conversationId).orElse(null);
        if (conversation != null && !conversation.getParticipantIds().contains(userId)) {
            conversation.getParticipantIds().add(userId);
            save(conversation);
            log.info("Added participant {} to conversation {}", userId, conversationId);
        }
    }

    /**
     * Remove participant from conversation
     */
    public void removeParticipant(String conversationId, String userId) {
        Conversation conversation = findById(conversationId).orElse(null);
        if (conversation != null) {
            conversation.getParticipantIds().remove(userId);
            save(conversation);
            log.info("Removed participant {} from conversation {}", userId, conversationId);
        }
    }

    /**
     * Update conversation name
     */
    public void updateName(String conversationId, String name) {
        Map<String, Object> updates = Map.of(
                "name", name,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, conversationId, updates);
        log.info("Updated name for conversation: {}", conversationId);
    }

    /**
     * Update conversation avatar
     */
    public void updateAvatar(String conversationId, String avatarUrl) {
        Map<String, Object> updates = Map.of(
                "avatarUrl", avatarUrl,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, conversationId, updates);
        log.info("Updated avatar for conversation: {}", conversationId);
    }

    /**
     * Set conversation active/inactive
     */
    public void setActive(String conversationId, boolean isActive) {
        Map<String, Object> updates = Map.of(
                "isActive", isActive,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, conversationId, updates);
        log.info("Set conversation {} active status: {}", conversationId, isActive);
    }

    /**
     * Increment message count
     */
    public void incrementMessageCount(String conversationId) {
        Conversation conversation = findById(conversationId).orElse(null);
        if (conversation != null) {
            long newCount = conversation.getMessageCount() + 1;
            Map<String, Object> updates = Map.of(
                    "messageCount", newCount,
                    "updatedAt", LocalDateTime.now()
            );
            firestoreService.updateDocument(COLLECTION_NAME, conversationId, updates);
        }
    }

    /**
     * Find conversations by type
     */
    public List<Conversation> findByType(ConversationType type) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "type", type, Conversation.class);
    }

    /**
     * Find active conversations for a user
     */
    public List<Conversation> findActiveByParticipantId(String userId) {
        List<Conversation> conversations = findByParticipantId(userId);
        return conversations.stream()
                .filter(Conversation::isActive)
                .toList();
    }

    /**
     * Delete conversation
     */
    public void delete(String conversationId) {
        firestoreService.deleteDocument(COLLECTION_NAME, conversationId);
        log.info("Deleted conversation: {}", conversationId);
    }
}
