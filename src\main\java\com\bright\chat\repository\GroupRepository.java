package com.bright.chat.repository;

import com.bright.chat.model.Group;
import com.bright.chat.model.GroupMember;
import com.bright.chat.model.GroupType;
import com.bright.chat.service.FirestoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Repository for Group operations in Firestore
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class GroupRepository {

    private static final String COLLECTION_NAME = "groups";
    
    private final FirestoreService firestoreService;

    /**
     * Save a new group
     */
    public String save(Group group) {
        group.setUpdatedAt(LocalDateTime.now());
        if (group.getCreatedAt() == null) {
            group.setCreatedAt(LocalDateTime.now());
        }
        
        if (group.getId() != null) {
            firestoreService.saveDocument(COLLECTION_NAME, group.getId(), group);
            log.info("Updated group: {}", group.getId());
            return group.getId();
        } else {
            String id = firestoreService.saveDocument(COLLECTION_NAME, group);
            log.info("Created new group: {}", id);
            return id;
        }
    }

    /**
     * Find group by ID
     */
    public Optional<Group> findById(String groupId) {
        Group group = firestoreService.getDocument(COLLECTION_NAME, groupId, Group.class);
        return Optional.ofNullable(group);
    }

    /**
     * Find groups by member ID
     */
    public List<Group> findByMemberId(String userId) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "memberIds", userId, Group.class);
    }

    /**
     * Find groups by owner ID
     */
    public List<Group> findByOwnerId(String ownerId) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "ownerId", ownerId, Group.class);
    }

    /**
     * Find groups by type
     */
    public List<Group> findByType(GroupType type) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "type", type, Group.class);
    }

    /**
     * Add member to group
     */
    public void addMember(String groupId, String userId, GroupMember member) {
        Group group = findById(groupId).orElse(null);
        if (group != null) {
            // Add to member list
            if (!group.getMemberIds().contains(userId)) {
                group.getMemberIds().add(userId);
            }
            
            // Add to members map
            if (group.getMembers() == null) {
                group.setMembers(Map.of(userId, member));
            } else {
                group.getMembers().put(userId, member);
            }
            
            // Update member count
            group.setMemberCount(group.getMemberIds().size());
            
            save(group);
            log.info("Added member {} to group {}", userId, groupId);
        }
    }

    /**
     * Remove member from group
     */
    public void removeMember(String groupId, String userId) {
        Group group = findById(groupId).orElse(null);
        if (group != null) {
            // Remove from member list
            group.getMemberIds().remove(userId);
            
            // Remove from members map
            if (group.getMembers() != null) {
                group.getMembers().remove(userId);
            }
            
            // Remove from admin list if present
            if (group.getAdminIds() != null) {
                group.getAdminIds().remove(userId);
            }
            
            // Update member count
            group.setMemberCount(group.getMemberIds().size());
            
            save(group);
            log.info("Removed member {} from group {}", userId, groupId);
        }
    }

    /**
     * Update group information
     */
    public void updateInfo(String groupId, String name, String description) {
        Map<String, Object> updates = Map.of(
                "name", name,
                "description", description,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, groupId, updates);
        log.info("Updated info for group: {}", groupId);
    }

    /**
     * Update group avatar
     */
    public void updateAvatar(String groupId, String avatarUrl) {
        Map<String, Object> updates = Map.of(
                "avatarUrl", avatarUrl,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, groupId, updates);
        log.info("Updated avatar for group: {}", groupId);
    }

    /**
     * Add admin to group
     */
    public void addAdmin(String groupId, String userId) {
        Group group = findById(groupId).orElse(null);
        if (group != null) {
            if (group.getAdminIds() == null) {
                group.setAdminIds(List.of(userId));
            } else if (!group.getAdminIds().contains(userId)) {
                group.getAdminIds().add(userId);
            }
            save(group);
            log.info("Added admin {} to group {}", userId, groupId);
        }
    }

    /**
     * Remove admin from group
     */
    public void removeAdmin(String groupId, String userId) {
        Group group = findById(groupId).orElse(null);
        if (group != null && group.getAdminIds() != null) {
            group.getAdminIds().remove(userId);
            save(group);
            log.info("Removed admin {} from group {}", userId, groupId);
        }
    }

    /**
     * Ban user from group
     */
    public void banUser(String groupId, String userId) {
        Group group = findById(groupId).orElse(null);
        if (group != null) {
            // Remove from members
            removeMember(groupId, userId);
            
            // Add to banned list
            if (group.getBannedUserIds() == null) {
                group.setBannedUserIds(List.of(userId));
            } else if (!group.getBannedUserIds().contains(userId)) {
                group.getBannedUserIds().add(userId);
            }
            
            save(group);
            log.info("Banned user {} from group {}", userId, groupId);
        }
    }

    /**
     * Unban user from group
     */
    public void unbanUser(String groupId, String userId) {
        Group group = findById(groupId).orElse(null);
        if (group != null && group.getBannedUserIds() != null) {
            group.getBannedUserIds().remove(userId);
            save(group);
            log.info("Unbanned user {} from group {}", userId, groupId);
        }
    }

    /**
     * Mute user in group
     */
    public void muteUser(String groupId, String userId, LocalDateTime muteUntil) {
        Group group = findById(groupId).orElse(null);
        if (group != null) {
            if (group.getMutedUserIds() == null) {
                group.setMutedUserIds(List.of(userId));
            } else if (!group.getMutedUserIds().contains(userId)) {
                group.getMutedUserIds().add(userId);
            }
            
            if (group.getMuteExpiry() == null) {
                group.setMuteExpiry(Map.of(userId, muteUntil));
            } else {
                group.getMuteExpiry().put(userId, muteUntil);
            }
            
            save(group);
            log.info("Muted user {} in group {} until {}", userId, groupId, muteUntil);
        }
    }

    /**
     * Unmute user in group
     */
    public void unmuteUser(String groupId, String userId) {
        Group group = findById(groupId).orElse(null);
        if (group != null) {
            if (group.getMutedUserIds() != null) {
                group.getMutedUserIds().remove(userId);
            }
            if (group.getMuteExpiry() != null) {
                group.getMuteExpiry().remove(userId);
            }
            save(group);
            log.info("Unmuted user {} in group {}", userId, groupId);
        }
    }

    /**
     * Update last activity
     */
    public void updateLastActivity(String groupId) {
        Map<String, Object> updates = Map.of(
                "lastActivity", LocalDateTime.now(),
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, groupId, updates);
    }

    /**
     * Generate new invite code
     */
    public void updateInviteCode(String groupId, String inviteCode, LocalDateTime expiry, int maxUses) {
        Map<String, Object> updates = Map.of(
                "inviteCode", inviteCode,
                "inviteCodeExpiry", expiry,
                "maxInviteUses", maxUses,
                "currentInviteUses", 0,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, groupId, updates);
        log.info("Updated invite code for group: {}", groupId);
    }

    /**
     * Increment invite code usage
     */
    public void incrementInviteUse(String groupId) {
        Group group = findById(groupId).orElse(null);
        if (group != null) {
            int newUseCount = group.getCurrentInviteUses() + 1;
            Map<String, Object> updates = Map.of(
                    "currentInviteUses", newUseCount,
                    "updatedAt", LocalDateTime.now()
            );
            firestoreService.updateDocument(COLLECTION_NAME, groupId, updates);
        }
    }

    /**
     * Set group active/inactive
     */
    public void setActive(String groupId, boolean isActive) {
        Map<String, Object> updates = Map.of(
                "isActive", isActive,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, groupId, updates);
        log.info("Set group {} active status: {}", groupId, isActive);
    }

    /**
     * Find public groups (for discovery)
     */
    public List<Group> findPublicGroups() {
        return findByType(GroupType.PUBLIC).stream()
                .filter(Group::isActive)
                .toList();
    }

    /**
     * Delete group
     */
    public void delete(String groupId) {
        firestoreService.deleteDocument(COLLECTION_NAME, groupId);
        log.info("Deleted group: {}", groupId);
    }
}
