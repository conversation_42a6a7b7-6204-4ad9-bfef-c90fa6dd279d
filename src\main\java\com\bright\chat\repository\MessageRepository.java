package com.bright.chat.repository;

import com.bright.chat.model.Message;
import com.bright.chat.model.MessageStatus;
import com.bright.chat.service.FirestoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Repository for Message operations in Firestore
 * Messages are stored as subcollections under conversations
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class MessageRepository {

    private static final String COLLECTION_NAME = "conversations";
    private static final String SUBCOLLECTION_NAME = "messages";
    
    private final FirestoreService firestoreService;

    /**
     * Save a new message
     */
    public String save(Message message) {
        message.setTimestamp(LocalDateTime.now());
        if (message.getStatus() == null) {
            message.setStatus(MessageStatus.SENT);
        }
        
        String messageId = firestoreService.saveToSubcollection(
                COLLECTION_NAME, 
                message.getConversationId(), 
                SUBCOLLECTION_NAME, 
                message
        );
        
        log.info("Saved message {} in conversation {}", messageId, message.getConversationId());
        return messageId;
    }

    /**
     * Find message by ID
     */
    public Optional<Message> findById(String conversationId, String messageId) {
        try {
            Message message = firestoreService.getDocument(
                    COLLECTION_NAME + "/" + conversationId + "/" + SUBCOLLECTION_NAME, 
                    messageId, 
                    Message.class
            );
            return Optional.ofNullable(message);
        } catch (Exception e) {
            log.error("Error finding message {} in conversation {}", messageId, conversationId, e);
            return Optional.empty();
        }
    }

    /**
     * Find messages in a conversation
     */
    public List<Message> findByConversationId(String conversationId) {
        return firestoreService.getFromSubcollection(
                COLLECTION_NAME, 
                conversationId, 
                SUBCOLLECTION_NAME, 
                Message.class
        );
    }

    /**
     * Find messages by sender
     */
    public List<Message> findBySenderId(String conversationId, String senderId) {
        // Note: This would require a custom query implementation in FirestoreService
        // For now, we'll get all messages and filter
        return findByConversationId(conversationId).stream()
                .filter(message -> senderId.equals(message.getSenderId()))
                .toList();
    }

    /**
     * Update message text (for editing)
     */
    public void updateText(String conversationId, String messageId, String newText) {
        Map<String, Object> updates = Map.of(
                "text", newText,
                "isEdited", true,
                "editedAt", LocalDateTime.now()
        );
        
        try {
            firestoreService.updateDocument(
                    COLLECTION_NAME + "/" + conversationId + "/" + SUBCOLLECTION_NAME, 
                    messageId, 
                    updates
            );
            log.info("Updated message {} in conversation {}", messageId, conversationId);
        } catch (Exception e) {
            log.error("Error updating message {} in conversation {}", messageId, conversationId, e);
        }
    }

    /**
     * Mark message as deleted
     */
    public void markAsDeleted(String conversationId, String messageId, String deletedBy) {
        Map<String, Object> updates = Map.of(
                "isDeleted", true,
                "deletedBy", deletedBy,
                "deletedAt", LocalDateTime.now()
        );
        
        try {
            firestoreService.updateDocument(
                    COLLECTION_NAME + "/" + conversationId + "/" + SUBCOLLECTION_NAME, 
                    messageId, 
                    updates
            );
            log.info("Marked message {} as deleted in conversation {}", messageId, conversationId);
        } catch (Exception e) {
            log.error("Error marking message {} as deleted in conversation {}", messageId, conversationId, e);
        }
    }

    /**
     * Update message status
     */
    public void updateStatus(String conversationId, String messageId, MessageStatus status) {
        Map<String, Object> updates = Map.of(
                "status", status
        );
        
        try {
            firestoreService.updateDocument(
                    COLLECTION_NAME + "/" + conversationId + "/" + SUBCOLLECTION_NAME, 
                    messageId, 
                    updates
            );
            log.info("Updated message {} status to {} in conversation {}", messageId, status, conversationId);
        } catch (Exception e) {
            log.error("Error updating message {} status in conversation {}", messageId, conversationId, e);
        }
    }

    /**
     * Mark message as read by user
     */
    public void markAsRead(String conversationId, String messageId, String userId) {
        Message message = findById(conversationId, messageId).orElse(null);
        if (message != null) {
            if (message.getReadBy() == null) {
                message.setReadBy(Map.of(userId, LocalDateTime.now()));
            } else {
                message.getReadBy().put(userId, LocalDateTime.now());
            }
            
            Map<String, Object> updates = Map.of(
                    "readBy", message.getReadBy()
            );
            
            try {
                firestoreService.updateDocument(
                        COLLECTION_NAME + "/" + conversationId + "/" + SUBCOLLECTION_NAME, 
                        messageId, 
                        updates
                );
                log.info("Marked message {} as read by user {} in conversation {}", messageId, userId, conversationId);
            } catch (Exception e) {
                log.error("Error marking message {} as read in conversation {}", messageId, conversationId, e);
            }
        }
    }

    /**
     * Add reaction to message
     */
    public void addReaction(String conversationId, String messageId, String emoji, String userId) {
        Message message = findById(conversationId, messageId).orElse(null);
        if (message != null) {
            if (message.getReactions() == null) {
                message.setReactions(Map.of(emoji, List.of(userId)));
            } else {
                List<String> users = message.getReactions().getOrDefault(emoji, List.of());
                if (!users.contains(userId)) {
                    users.add(userId);
                    message.getReactions().put(emoji, users);
                }
            }
            
            Map<String, Object> updates = Map.of(
                    "reactions", message.getReactions()
            );
            
            try {
                firestoreService.updateDocument(
                        COLLECTION_NAME + "/" + conversationId + "/" + SUBCOLLECTION_NAME, 
                        messageId, 
                        updates
                );
                log.info("Added reaction {} by user {} to message {} in conversation {}", emoji, userId, messageId, conversationId);
            } catch (Exception e) {
                log.error("Error adding reaction to message {} in conversation {}", messageId, conversationId, e);
            }
        }
    }

    /**
     * Remove reaction from message
     */
    public void removeReaction(String conversationId, String messageId, String emoji, String userId) {
        Message message = findById(conversationId, messageId).orElse(null);
        if (message != null && message.getReactions() != null) {
            List<String> users = message.getReactions().get(emoji);
            if (users != null) {
                users.remove(userId);
                if (users.isEmpty()) {
                    message.getReactions().remove(emoji);
                } else {
                    message.getReactions().put(emoji, users);
                }
                
                Map<String, Object> updates = Map.of(
                        "reactions", message.getReactions()
                );
                
                try {
                    firestoreService.updateDocument(
                            COLLECTION_NAME + "/" + conversationId + "/" + SUBCOLLECTION_NAME, 
                            messageId, 
                            updates
                    );
                    log.info("Removed reaction {} by user {} from message {} in conversation {}", emoji, userId, messageId, conversationId);
                } catch (Exception e) {
                    log.error("Error removing reaction from message {} in conversation {}", messageId, conversationId, e);
                }
            }
        }
    }

    /**
     * Get recent messages in conversation (for pagination)
     */
    public List<Message> findRecentMessages(String conversationId, int limit) {
        // Note: This would require implementing pagination in FirestoreService
        // For now, we'll get all messages and limit
        return findByConversationId(conversationId).stream()
                .sorted((m1, m2) -> m2.getTimestamp().compareTo(m1.getTimestamp()))
                .limit(limit)
                .toList();
    }

    /**
     * Delete message permanently
     */
    public void delete(String conversationId, String messageId) {
        try {
            firestoreService.deleteDocument(
                    COLLECTION_NAME + "/" + conversationId + "/" + SUBCOLLECTION_NAME, 
                    messageId
            );
            log.info("Deleted message {} from conversation {}", messageId, conversationId);
        } catch (Exception e) {
            log.error("Error deleting message {} from conversation {}", messageId, conversationId, e);
        }
    }
}
