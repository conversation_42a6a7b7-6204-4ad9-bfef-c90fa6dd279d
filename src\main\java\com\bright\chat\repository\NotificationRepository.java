package com.bright.chat.repository;

import com.bright.chat.model.Notification;
import com.bright.chat.model.NotificationStatus;
import com.bright.chat.model.NotificationType;
import com.bright.chat.service.FirestoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Repository for Notification operations in Firestore
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class NotificationRepository {

    private static final String COLLECTION_NAME = "notifications";
    
    private final FirestoreService firestoreService;

    /**
     * Save a new notification
     */
    public String save(Notification notification) {
        notification.setCreatedAt(LocalDateTime.now());
        notification.setUpdatedAt(LocalDateTime.now());
        
        if (notification.getStatus() == null) {
            notification.setStatus(NotificationStatus.PENDING);
        }
        
        if (notification.getId() != null) {
            firestoreService.saveDocument(COLLECTION_NAME, notification.getId(), notification);
            log.info("Updated notification: {}", notification.getId());
            return notification.getId();
        } else {
            String id = firestoreService.saveDocument(COLLECTION_NAME, notification);
            log.info("Created new notification: {}", id);
            return id;
        }
    }

    /**
     * Find notification by ID
     */
    public Optional<Notification> findById(String notificationId) {
        Notification notification = firestoreService.getDocument(COLLECTION_NAME, notificationId, Notification.class);
        return Optional.ofNullable(notification);
    }

    /**
     * Find notifications by user ID
     */
    public List<Notification> findByUserId(String userId) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "userId", userId, Notification.class);
    }

    /**
     * Find notifications by status
     */
    public List<Notification> findByStatus(NotificationStatus status) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "status", status, Notification.class);
    }

    /**
     * Find notifications by type
     */
    public List<Notification> findByType(NotificationType type) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "type", type, Notification.class);
    }

    /**
     * Find pending notifications for retry
     */
    public List<Notification> findPendingForRetry() {
        List<Notification> pendingNotifications = findByStatus(NotificationStatus.PENDING);
        LocalDateTime now = LocalDateTime.now();
        
        return pendingNotifications.stream()
                .filter(notification -> notification.getNextRetryAt() == null || 
                                      notification.getNextRetryAt().isBefore(now))
                .toList();
    }

    /**
     * Find failed notifications for retry
     */
    public List<Notification> findFailedForRetry() {
        List<Notification> failedNotifications = findByStatus(NotificationStatus.FAILED);
        LocalDateTime now = LocalDateTime.now();
        
        return failedNotifications.stream()
                .filter(notification -> notification.getRetryCount() < 3) // Max 3 retries
                .filter(notification -> notification.getNextRetryAt() == null || 
                                      notification.getNextRetryAt().isBefore(now))
                .toList();
    }

    /**
     * Update notification status
     */
    public void updateStatus(String notificationId, NotificationStatus status) {
        Map<String, Object> updates = Map.of(
                "status", status,
                "updatedAt", LocalDateTime.now()
        );
        
        // Add timestamp based on status
        switch (status) {
            case SENT -> updates.put("sentAt", LocalDateTime.now());
            case DELIVERED -> updates.put("deliveredAt", LocalDateTime.now());
            case READ -> updates.put("readAt", LocalDateTime.now());
        }
        
        firestoreService.updateDocument(COLLECTION_NAME, notificationId, updates);
        log.info("Updated notification {} status to {}", notificationId, status);
    }

    /**
     * Update FCM message ID after sending
     */
    public void updateFcmMessageId(String notificationId, String fcmMessageId) {
        Map<String, Object> updates = Map.of(
                "fcmMessageId", fcmMessageId,
                "status", NotificationStatus.SENT,
                "sentAt", LocalDateTime.now(),
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, notificationId, updates);
        log.info("Updated FCM message ID for notification: {}", notificationId);
    }

    /**
     * Mark notification as failed with reason
     */
    public void markAsFailed(String notificationId, String failureReason) {
        Notification notification = findById(notificationId).orElse(null);
        if (notification != null) {
            int newRetryCount = notification.getRetryCount() + 1;
            LocalDateTime nextRetry = LocalDateTime.now().plusMinutes(newRetryCount * 5); // Exponential backoff
            
            Map<String, Object> updates = Map.of(
                    "status", NotificationStatus.FAILED,
                    "failureReason", failureReason,
                    "retryCount", newRetryCount,
                    "nextRetryAt", nextRetry,
                    "updatedAt", LocalDateTime.now()
            );
            firestoreService.updateDocument(COLLECTION_NAME, notificationId, updates);
            log.info("Marked notification {} as failed: {}", notificationId, failureReason);
        }
    }

    /**
     * Mark notification as expired
     */
    public void markAsExpired(String notificationId) {
        Map<String, Object> updates = Map.of(
                "status", NotificationStatus.EXPIRED,
                "updatedAt", LocalDateTime.now()
        );
        firestoreService.updateDocument(COLLECTION_NAME, notificationId, updates);
        log.info("Marked notification {} as expired", notificationId);
    }

    /**
     * Find unread notifications for user
     */
    public List<Notification> findUnreadByUserId(String userId) {
        return findByUserId(userId).stream()
                .filter(notification -> notification.getReadAt() == null)
                .filter(notification -> notification.getStatus() == NotificationStatus.DELIVERED ||
                                      notification.getStatus() == NotificationStatus.SENT)
                .toList();
    }

    /**
     * Mark notification as read
     */
    public void markAsRead(String notificationId) {
        updateStatus(notificationId, NotificationStatus.READ);
    }

    /**
     * Find notifications by conversation
     */
    public List<Notification> findByConversationId(String conversationId) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "conversationId", conversationId, Notification.class);
    }

    /**
     * Find notifications by group
     */
    public List<Notification> findByGroupId(String groupId) {
        return firestoreService.queryDocuments(COLLECTION_NAME, "groupId", groupId, Notification.class);
    }

    /**
     * Delete old notifications (cleanup)
     */
    public void deleteOldNotifications(LocalDateTime before) {
        List<Notification> allNotifications = firestoreService.getAllDocuments(COLLECTION_NAME, Notification.class);
        
        List<String> toDelete = allNotifications.stream()
                .filter(notification -> notification.getCreatedAt().isBefore(before))
                .filter(notification -> notification.getStatus() == NotificationStatus.READ ||
                                      notification.getStatus() == NotificationStatus.EXPIRED)
                .map(Notification::getId)
                .toList();
        
        for (String id : toDelete) {
            firestoreService.deleteDocument(COLLECTION_NAME, id);
        }
        
        log.info("Deleted {} old notifications", toDelete.size());
    }

    /**
     * Get notification statistics for user
     */
    public Map<NotificationStatus, Long> getNotificationStats(String userId) {
        List<Notification> userNotifications = findByUserId(userId);
        
        return userNotifications.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                        Notification::getStatus,
                        java.util.stream.Collectors.counting()
                ));
    }

    /**
     * Delete notification
     */
    public void delete(String notificationId) {
        firestoreService.deleteDocument(COLLECTION_NAME, notificationId);
        log.info("Deleted notification: {}", notificationId);
    }
}
