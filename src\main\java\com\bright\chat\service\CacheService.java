package com.bright.chat.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Service for managing Redis cache operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CacheService {

    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * Get cache statistics
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        Collection<String> cacheNames = cacheManager.getCacheNames();
        stats.put("totalCaches", cacheNames.size());
        stats.put("cacheNames", cacheNames);
        
        Map<String, Object> cacheDetails = new HashMap<>();
        for (String cacheName : cacheNames) {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Map<String, Object> cacheInfo = new HashMap<>();
                cacheInfo.put("name", cacheName);
                cacheInfo.put("nativeCache", cache.getNativeCache().getClass().getSimpleName());
                cacheDetails.put(cacheName, cacheInfo);
            }
        }
        stats.put("cacheDetails", cacheDetails);
        
        return stats;
    }

    /**
     * Get Redis info
     */
    public Map<String, Object> getRedisInfo() {
        Map<String, Object> info = new HashMap<>();
        
        try {
            // Get Redis connection info
            info.put("connected", redisTemplate.getConnectionFactory().getConnection().ping() != null);
            
            // Get key count for each cache pattern
            Set<String> userKeys = redisTemplate.keys("users::*");
            Set<String> userBatchKeys = redisTemplate.keys("usersBatch::*");
            Set<String> userSearchKeys = redisTemplate.keys("userSearch::*");
            Set<String> userValidationKeys = redisTemplate.keys("userValidation::*");
            
            info.put("userCacheKeys", userKeys != null ? userKeys.size() : 0);
            info.put("userBatchCacheKeys", userBatchKeys != null ? userBatchKeys.size() : 0);
            info.put("userSearchCacheKeys", userSearchKeys != null ? userSearchKeys.size() : 0);
            info.put("userValidationCacheKeys", userValidationKeys != null ? userValidationKeys.size() : 0);
            
            // Total keys
            Set<String> allKeys = redisTemplate.keys("*");
            info.put("totalKeys", allKeys != null ? allKeys.size() : 0);
            
        } catch (Exception e) {
            log.error("Error getting Redis info", e);
            info.put("connected", false);
            info.put("error", e.getMessage());
        }
        
        return info;
    }

    /**
     * Clear specific cache
     */
    public boolean clearCache(String cacheName) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                log.info("Cleared cache: {}", cacheName);
                return true;
            } else {
                log.warn("Cache not found: {}", cacheName);
                return false;
            }
        } catch (Exception e) {
            log.error("Error clearing cache: {}", cacheName, e);
            return false;
        }
    }

    /**
     * Clear all caches
     */
    public void clearAllCaches() {
        try {
            Collection<String> cacheNames = cacheManager.getCacheNames();
            for (String cacheName : cacheNames) {
                clearCache(cacheName);
            }
            log.info("Cleared all caches: {}", cacheNames);
        } catch (Exception e) {
            log.error("Error clearing all caches", e);
        }
    }

    /**
     * Get cache entry
     */
    public Object getCacheEntry(String cacheName, String key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Cache.ValueWrapper wrapper = cache.get(key);
                return wrapper != null ? wrapper.get() : null;
            }
            return null;
        } catch (Exception e) {
            log.error("Error getting cache entry: {}:{}", cacheName, key, e);
            return null;
        }
    }

    /**
     * Put cache entry with TTL
     */
    public void putCacheEntry(String cacheName, String key, Object value) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.put(key, value);
                log.debug("Put cache entry: {}:{}", cacheName, key);
            }
        } catch (Exception e) {
            log.error("Error putting cache entry: {}:{}", cacheName, key, e);
        }
    }

    /**
     * Evict cache entry
     */
    public void evictCacheEntry(String cacheName, String key) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.evict(key);
                log.debug("Evicted cache entry: {}:{}", cacheName, key);
            }
        } catch (Exception e) {
            log.error("Error evicting cache entry: {}:{}", cacheName, key, e);
        }
    }

    /**
     * Set TTL for a Redis key
     */
    public boolean setTtl(String key, long timeout, TimeUnit timeUnit) {
        try {
            return redisTemplate.expire(key, timeout, timeUnit);
        } catch (Exception e) {
            log.error("Error setting TTL for key: {}", key, e);
            return false;
        }
    }

    /**
     * Get TTL for a Redis key
     */
    public long getTtl(String key) {
        try {
            return redisTemplate.getExpire(key, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error getting TTL for key: {}", key, e);
            return -1;
        }
    }

    /**
     * Check if key exists in Redis
     */
    public boolean keyExists(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("Error checking key existence: {}", key, e);
            return false;
        }
    }

    /**
     * Get all keys matching pattern
     */
    public Set<String> getKeys(String pattern) {
        try {
            return redisTemplate.keys(pattern);
        } catch (Exception e) {
            log.error("Error getting keys with pattern: {}", pattern, e);
            return Set.of();
        }
    }

    /**
     * Warm up user cache
     */
    public void warmUpUserCache(String userId) {
        try {
            // This will trigger the cache if not already cached
            log.info("Warming up cache for user: {}", userId);
            // The actual warming up would be done by calling the cached method
            // This is just a placeholder for the warming up logic
        } catch (Exception e) {
            log.error("Error warming up cache for user: {}", userId, e);
        }
    }
}
