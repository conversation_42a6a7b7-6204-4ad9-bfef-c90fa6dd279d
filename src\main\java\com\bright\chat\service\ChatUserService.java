package com.bright.chat.service;

import com.bright.chat.model.ChatUser;
import com.bright.chat.model.UserInfo;
import com.bright.chat.model.UserStatus;
import com.bright.chat.repository.ChatUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing chat users
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChatUserService {

    private final ChatUserRepository chatUserRepository;
    private final UserServiceClient userServiceClient;

    /**
     * Create or update chat user from User Service data
     */
    public ChatUser createOrUpdateChatUser(String userId, String fcmToken) {
        // Get user info from User Service
        Optional<UserInfo> userInfoOpt = userServiceClient.getUserById(userId);
        if (userInfoOpt.isEmpty()) {
            throw new RuntimeException("User not found in User Service: " + userId);
        }

        UserInfo userInfo = userInfoOpt.get();
        
        // Check if chat user already exists
        Optional<ChatUser> existingChatUser = chatUserRepository.findByUserId(userId);
        
        ChatUser chatUser;
        if (existingChatUser.isPresent()) {
            // Update existing chat user
            chatUser = existingChatUser.get();
            chatUser.setDisplayName(userInfo.getDisplayName());
            chatUser.setAvatarUrl(userInfo.getAvatarUrl());
            chatUser.setFcmToken(fcmToken);
            chatUser.setLastSyncedAt(LocalDateTime.now());
        } else {
            // Create new chat user
            chatUser = ChatUser.builder()
                    .userId(userId)
                    .displayName(userInfo.getDisplayName())
                    .avatarUrl(userInfo.getAvatarUrl())
                    .fcmToken(fcmToken)
                    .status(UserStatus.OFFLINE)
                    .isOnline(false)
                    .allowNotifications(true)
                    .timezone("UTC")
                    .lastSyncedAt(LocalDateTime.now())
                    .build();
        }

        chatUserRepository.save(chatUser);
        log.info("Created/updated chat user: {}", userId);
        return chatUser;
    }

    /**
     * Get chat user by user ID
     */
    public Optional<ChatUser> getChatUser(String userId) {
        return chatUserRepository.findByUserId(userId);
    }

    /**
     * Get multiple chat users
     */
    public List<ChatUser> getChatUsers(List<String> userIds) {
        return chatUserRepository.findByUserIds(userIds);
    }

    /**
     * Update FCM token
     */
    public void updateFcmToken(String userId, String fcmToken) {
        chatUserRepository.updateFcmToken(userId, fcmToken);
        log.info("Updated FCM token for user: {}", userId);
    }

    /**
     * Set user online status
     */
    public void setUserOnline(String userId) {
        chatUserRepository.updateUserStatus(userId, true);
        log.info("Set user {} online", userId);
    }

    /**
     * Set user offline status
     */
    public void setUserOffline(String userId) {
        chatUserRepository.updateUserStatus(userId, false);
        log.info("Set user {} offline", userId);
    }

    /**
     * Update user status
     */
    public void updateUserStatus(String userId, UserStatus status) {
        Optional<ChatUser> chatUserOpt = chatUserRepository.findByUserId(userId);
        if (chatUserOpt.isPresent()) {
            ChatUser chatUser = chatUserOpt.get();
            chatUser.setStatus(status);
            chatUser.setLastSeen(LocalDateTime.now());
            chatUserRepository.save(chatUser);
            log.info("Updated status for user {}: {}", userId, status);
        }
    }

    /**
     * Block a user
     */
    public void blockUser(String userId, String userToBlock) {
        // Validate that user to block exists
        if (!userServiceClient.userExists(userToBlock)) {
            throw new RuntimeException("User to block not found: " + userToBlock);
        }

        chatUserRepository.blockUser(userId, userToBlock);
        log.info("User {} blocked user {}", userId, userToBlock);
    }

    /**
     * Unblock a user
     */
    public void unblockUser(String userId, String userToUnblock) {
        chatUserRepository.unblockUser(userId, userToUnblock);
        log.info("User {} unblocked user {}", userId, userToUnblock);
    }

    /**
     * Check if user is blocked
     */
    public boolean isUserBlocked(String userId, String potentialBlockedUserId) {
        return chatUserRepository.isUserBlocked(userId, potentialBlockedUserId);
    }

    /**
     * Get blocked users list
     */
    public List<String> getBlockedUsers(String userId) {
        Optional<ChatUser> chatUserOpt = chatUserRepository.findByUserId(userId);
        return chatUserOpt.map(ChatUser::getBlockedUserIds).orElse(List.of());
    }

    /**
     * Sync user display information from User Service
     */
    public void syncUserDisplayInfo(String userId) {
        Optional<UserInfo> userInfoOpt = userServiceClient.getUserById(userId);
        if (userInfoOpt.isPresent()) {
            UserInfo userInfo = userInfoOpt.get();
            chatUserRepository.updateDisplayInfo(userId, userInfo.getDisplayName(), userInfo.getAvatarUrl());
            log.info("Synced display info for user: {}", userId);
        } else {
            log.warn("Could not sync display info - user not found in User Service: {}", userId);
        }
    }

    /**
     * Sync multiple users display information
     */
    public void syncUsersDisplayInfo(List<String> userIds) {
        List<UserInfo> userInfos = userServiceClient.getUsersByIds(userIds);
        
        for (UserInfo userInfo : userInfos) {
            chatUserRepository.updateDisplayInfo(
                    userInfo.getId(), 
                    userInfo.getDisplayName(), 
                    userInfo.getAvatarUrl()
            );
        }
        
        log.info("Synced display info for {} users", userInfos.size());
    }

    /**
     * Update chat preferences
     */
    public void updateChatPreferences(String userId, boolean allowNotifications, String timezone) {
        Optional<ChatUser> chatUserOpt = chatUserRepository.findByUserId(userId);
        if (chatUserOpt.isPresent()) {
            ChatUser chatUser = chatUserOpt.get();
            chatUser.setAllowNotifications(allowNotifications);
            chatUser.setTimezone(timezone);
            chatUserRepository.save(chatUser);
            log.info("Updated chat preferences for user: {}", userId);
        }
    }

    /**
     * Add conversation to user
     */
    public void addConversation(String userId, String conversationId) {
        chatUserRepository.addConversation(userId, conversationId);
    }

    /**
     * Remove conversation from user
     */
    public void removeConversation(String userId, String conversationId) {
        chatUserRepository.removeConversation(userId, conversationId);
    }

    /**
     * Add group to user
     */
    public void addGroup(String userId, String groupId) {
        chatUserRepository.addGroup(userId, groupId);
    }

    /**
     * Remove group from user
     */
    public void removeGroup(String userId, String groupId) {
        chatUserRepository.removeGroup(userId, groupId);
    }

    /**
     * Get user's conversations
     */
    public List<String> getUserConversations(String userId) {
        Optional<ChatUser> chatUserOpt = chatUserRepository.findByUserId(userId);
        return chatUserOpt.map(ChatUser::getConversationIds).orElse(List.of());
    }

    /**
     * Get user's groups
     */
    public List<String> getUserGroups(String userId) {
        Optional<ChatUser> chatUserOpt = chatUserRepository.findByUserId(userId);
        return chatUserOpt.map(ChatUser::getGroupIds).orElse(List.of());
    }

    /**
     * Validate user exists in User Service
     */
    public boolean validateUser(String userId) {
        return userServiceClient.userExists(userId);
    }

    /**
     * Validate multiple users exist in User Service
     */
    public List<String> validateUsers(List<String> userIds) {
        return userServiceClient.validateUserIds(userIds);
    }

    /**
     * Search users (delegates to User Service)
     */
    public List<UserInfo> searchUsers(String query, int limit) {
        return userServiceClient.searchUsers(query, limit);
    }

    /**
     * Delete chat user
     */
    public void deleteChatUser(String userId) {
        chatUserRepository.delete(userId);
        log.info("Deleted chat user: {}", userId);
    }
}
