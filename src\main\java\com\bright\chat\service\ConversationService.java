package com.bright.chat.service;

import com.bright.chat.model.*;
import com.bright.chat.repository.ConversationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service for managing conversations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ConversationService {

    private final ConversationRepository conversationRepository;
    private final ChatUserService chatUserService;
    private final NotificationService notificationService;

    /**
     * Create a direct conversation between two users
     */
    public Conversation createDirectConversation(String user1Id, String user2Id) {
        // Validate users exist
        if (!chatUserService.validateUser(user1Id) || !chatUserService.validateUser(user2Id)) {
            throw new RuntimeException("One or both users not found");
        }

        // Check if conversation already exists
        Optional<Conversation> existingConv = conversationRepository.findDirectConversation(user1Id, user2Id);
        if (existingConv.isPresent()) {
            log.info("Direct conversation already exists between {} and {}", user1Id, user2Id);
            return existingConv.get();
        }

        // Create new conversation
        Conversation conversation = Conversation.builder()
                .type(ConversationType.DIRECT)
                .participantIds(List.of(user1Id, user2Id))
                .isActive(true)
                .messageCount(0)
                .build();

        String conversationId = conversationRepository.save(conversation);
        conversation.setId(conversationId);

        // Add conversation to users
        chatUserService.addConversation(user1Id, conversationId);
        chatUserService.addConversation(user2Id, conversationId);

        log.info("Created direct conversation {} between {} and {}", conversationId, user1Id, user2Id);
        return conversation;
    }

    /**
     * Create a group conversation
     */
    public Conversation createGroupConversation(String creatorId, String name, String description, 
                                              List<String> participantIds) {
        // Validate creator exists
        if (!chatUserService.validateUser(creatorId)) {
            throw new RuntimeException("Creator not found: " + creatorId);
        }

        // Validate all participants exist
        List<String> validParticipants = chatUserService.validateUsers(participantIds);
        if (validParticipants.size() != participantIds.size()) {
            throw new RuntimeException("Some participants not found");
        }

        // Add creator to participants if not already included
        if (!validParticipants.contains(creatorId)) {
            validParticipants.add(creatorId);
        }

        // Create conversation
        Conversation conversation = Conversation.builder()
                .type(ConversationType.GROUP)
                .name(name)
                .description(description)
                .participantIds(validParticipants)
                .isActive(true)
                .messageCount(0)
                .build();

        String conversationId = conversationRepository.save(conversation);
        conversation.setId(conversationId);

        // Add conversation to all participants
        for (String participantId : validParticipants) {
            chatUserService.addConversation(participantId, conversationId);
        }

        log.info("Created group conversation {} with {} participants", conversationId, validParticipants.size());
        return conversation;
    }

    /**
     * Get conversation by ID
     */
    public Optional<Conversation> getConversation(String conversationId) {
        return conversationRepository.findById(conversationId);
    }

    /**
     * Get conversations for a user
     */
    public List<Conversation> getUserConversations(String userId) {
        return conversationRepository.findActiveByParticipantId(userId);
    }

    /**
     * Add participant to conversation
     */
    public void addParticipant(String conversationId, String userId, String addedBy) {
        Optional<Conversation> convOpt = conversationRepository.findById(conversationId);
        if (convOpt.isEmpty()) {
            throw new RuntimeException("Conversation not found: " + conversationId);
        }

        Conversation conversation = convOpt.get();
        
        // Check if user is already a participant
        if (conversation.getParticipantIds().contains(userId)) {
            log.warn("User {} is already a participant in conversation {}", userId, conversationId);
            return;
        }

        // Validate user exists
        if (!chatUserService.validateUser(userId)) {
            throw new RuntimeException("User not found: " + userId);
        }

        // Add participant
        conversationRepository.addParticipant(conversationId, userId);
        chatUserService.addConversation(userId, conversationId);

        // Send notification to new participant
        notificationService.sendGroupInviteNotification(userId, conversationId, addedBy);

        log.info("Added participant {} to conversation {}", userId, conversationId);
    }

    /**
     * Remove participant from conversation
     */
    public void removeParticipant(String conversationId, String userId, String removedBy) {
        Optional<Conversation> convOpt = conversationRepository.findById(conversationId);
        if (convOpt.isEmpty()) {
            throw new RuntimeException("Conversation not found: " + conversationId);
        }

        Conversation conversation = convOpt.get();
        
        // Check if user is a participant
        if (!conversation.getParticipantIds().contains(userId)) {
            log.warn("User {} is not a participant in conversation {}", userId, conversationId);
            return;
        }

        // Remove participant
        conversationRepository.removeParticipant(conversationId, userId);
        chatUserService.removeConversation(userId, conversationId);

        // If it's a direct conversation and one user leaves, deactivate it
        if (conversation.getType() == ConversationType.DIRECT) {
            conversationRepository.setActive(conversationId, false);
        }

        log.info("Removed participant {} from conversation {}", userId, conversationId);
    }

    /**
     * Update conversation name
     */
    public void updateConversationName(String conversationId, String name, String updatedBy) {
        Optional<Conversation> convOpt = conversationRepository.findById(conversationId);
        if (convOpt.isEmpty()) {
            throw new RuntimeException("Conversation not found: " + conversationId);
        }

        Conversation conversation = convOpt.get();
        
        // Only allow name updates for group conversations
        if (conversation.getType() != ConversationType.GROUP) {
            throw new RuntimeException("Cannot update name for direct conversations");
        }

        conversationRepository.updateName(conversationId, name);
        log.info("Updated conversation {} name to: {}", conversationId, name);
    }

    /**
     * Update conversation avatar
     */
    public void updateConversationAvatar(String conversationId, String avatarUrl, String updatedBy) {
        Optional<Conversation> convOpt = conversationRepository.findById(conversationId);
        if (convOpt.isEmpty()) {
            throw new RuntimeException("Conversation not found: " + conversationId);
        }

        conversationRepository.updateAvatar(conversationId, avatarUrl);
        log.info("Updated conversation {} avatar", conversationId);
    }

    /**
     * Update last message information
     */
    public void updateLastMessage(String conversationId, String messageId, String messageText, 
                                 String senderId, MessageType messageType) {
        conversationRepository.updateLastMessage(conversationId, messageId, messageText, senderId, LocalDateTime.now());
        conversationRepository.incrementMessageCount(conversationId);
        log.info("Updated last message for conversation: {}", conversationId);
    }

    /**
     * Check if user is participant in conversation
     */
    public boolean isParticipant(String conversationId, String userId) {
        Optional<Conversation> convOpt = conversationRepository.findById(conversationId);
        return convOpt.map(conv -> conv.getParticipantIds().contains(userId)).orElse(false);
    }

    /**
     * Get conversation participants
     */
    public List<String> getParticipants(String conversationId) {
        Optional<Conversation> convOpt = conversationRepository.findById(conversationId);
        return convOpt.map(Conversation::getParticipantIds).orElse(List.of());
    }

    /**
     * Archive conversation for user
     */
    public void archiveConversation(String conversationId, String userId) {
        // This would be implemented with user-specific conversation settings
        // For now, we'll just remove it from user's active conversations
        chatUserService.removeConversation(userId, conversationId);
        log.info("Archived conversation {} for user {}", conversationId, userId);
    }

    /**
     * Mute conversation for user
     */
    public void muteConversation(String conversationId, String userId, boolean muted) {
        // This would be implemented with user-specific conversation settings
        // For now, we'll log the action
        log.info("User {} {} conversation {}", userId, muted ? "muted" : "unmuted", conversationId);
    }

    /**
     * Delete conversation
     */
    public void deleteConversation(String conversationId, String deletedBy) {
        Optional<Conversation> convOpt = conversationRepository.findById(conversationId);
        if (convOpt.isEmpty()) {
            throw new RuntimeException("Conversation not found: " + conversationId);
        }

        Conversation conversation = convOpt.get();
        
        // Remove conversation from all participants
        for (String participantId : conversation.getParticipantIds()) {
            chatUserService.removeConversation(participantId, conversationId);
        }

        // Delete the conversation
        conversationRepository.delete(conversationId);
        log.info("Deleted conversation {} by user {}", conversationId, deletedBy);
    }

    /**
     * Get conversation statistics
     */
    public Map<String, Object> getConversationStats(String conversationId) {
        Optional<Conversation> convOpt = conversationRepository.findById(conversationId);
        if (convOpt.isEmpty()) {
            throw new RuntimeException("Conversation not found: " + conversationId);
        }

        Conversation conversation = convOpt.get();
        
        return Map.of(
                "id", conversation.getId(),
                "type", conversation.getType(),
                "participantCount", conversation.getParticipantIds().size(),
                "messageCount", conversation.getMessageCount(),
                "isActive", conversation.isActive(),
                "createdAt", conversation.getCreatedAt(),
                "lastActivity", conversation.getLastMessageTimestamp()
        );
    }
}
