package com.bright.chat.service;

import com.google.firebase.messaging.*;
import com.bright.chat.model.Notification;
import com.bright.chat.model.NotificationPriority;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Service for Firebase Cloud Messaging (FCM) operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FCMService {

    private final FirebaseMessaging firebaseMessaging;

    /**
     * Send a notification to a single device
     */
    public String sendNotification(String fcmToken, String title, String body, Map<String, String> data) {
        try {
            Message.Builder messageBuilder = Message.builder()
                    .setToken(fcmToken)
                    .setNotification(
                            com.google.firebase.messaging.Notification.builder()
                                    .setTitle(title)
                                    .setBody(body)
                                    .build()
                    );

            if (data != null && !data.isEmpty()) {
                messageBuilder.putAllData(data);
            }

            Message message = messageBuilder.build();
            String response = firebaseMessaging.send(message);
            log.info("Successfully sent message: {}", response);
            return response;
        } catch (FirebaseMessagingException e) {
            log.error("Error sending FCM message to token: {}", fcmToken, e);
            throw new RuntimeException("Failed to send FCM message", e);
        }
    }

    /**
     * Send notification using Notification model
     */
    public String sendNotification(Notification notification) {
        try {
            Message.Builder messageBuilder = Message.builder()
                    .setToken(notification.getFcmToken())
                    .setNotification(
                            com.google.firebase.messaging.Notification.builder()
                                    .setTitle(notification.getTitle())
                                    .setBody(notification.getBody())
                                    .setImage(notification.getImageUrl())
                                    .build()
                    );

            // Add custom data
            if (notification.getData() != null && !notification.getData().isEmpty()) {
                messageBuilder.putAllData(notification.getData());
            }

            // Set Android-specific options
            AndroidConfig.Builder androidConfigBuilder = AndroidConfig.builder()
                    .setPriority(mapPriority(notification.getPriority()));

            if (notification.getClickAction() != null) {
                androidConfigBuilder.setNotification(
                        AndroidNotification.builder()
                                .setClickAction(notification.getClickAction())
                                .setSound(notification.getSound())
                                .build()
                );
            }

            messageBuilder.setAndroidConfig(androidConfigBuilder.build());

            // Set iOS-specific options
            ApnsConfig.Builder apnsConfigBuilder = ApnsConfig.builder()
                    .setAps(Aps.builder()
                            .setSound(notification.getSound())
                            .build());

            messageBuilder.setApnsConfig(apnsConfigBuilder.build());

            Message message = messageBuilder.build();
            String response = firebaseMessaging.send(message);
            log.info("Successfully sent notification: {}", response);
            return response;
        } catch (FirebaseMessagingException e) {
            log.error("Error sending FCM notification: {}", notification.getId(), e);
            throw new RuntimeException("Failed to send FCM notification", e);
        }
    }

    /**
     * Send notifications to multiple devices
     */
    public BatchResponse sendMulticastNotification(List<String> fcmTokens, String title, String body, Map<String, String> data) {
        try {
            MulticastMessage.Builder messageBuilder = MulticastMessage.builder()
                    .addAllTokens(fcmTokens)
                    .setNotification(
                            com.google.firebase.messaging.Notification.builder()
                                    .setTitle(title)
                                    .setBody(body)
                                    .build()
                    );

            if (data != null && !data.isEmpty()) {
                messageBuilder.putAllData(data);
            }

            MulticastMessage message = messageBuilder.build();
            BatchResponse response = firebaseMessaging.sendMulticast(message);
            
            log.info("Successfully sent multicast message. Success count: {}, Failure count: {}", 
                    response.getSuccessCount(), response.getFailureCount());
            
            // Log failed tokens
            if (response.getFailureCount() > 0) {
                List<SendResponse> responses = response.getResponses();
                for (int i = 0; i < responses.size(); i++) {
                    if (!responses.get(i).isSuccessful()) {
                        log.warn("Failed to send to token {}: {}", 
                                fcmTokens.get(i), responses.get(i).getException().getMessage());
                    }
                }
            }
            
            return response;
        } catch (FirebaseMessagingException e) {
            log.error("Error sending multicast FCM message", e);
            throw new RuntimeException("Failed to send multicast FCM message", e);
        }
    }

    /**
     * Send notification to a topic
     */
    public String sendToTopic(String topic, String title, String body, Map<String, String> data) {
        try {
            Message.Builder messageBuilder = Message.builder()
                    .setTopic(topic)
                    .setNotification(
                            com.google.firebase.messaging.Notification.builder()
                                    .setTitle(title)
                                    .setBody(body)
                                    .build()
                    );

            if (data != null && !data.isEmpty()) {
                messageBuilder.putAllData(data);
            }

            Message message = messageBuilder.build();
            String response = firebaseMessaging.send(message);
            log.info("Successfully sent message to topic {}: {}", topic, response);
            return response;
        } catch (FirebaseMessagingException e) {
            log.error("Error sending FCM message to topic: {}", topic, e);
            throw new RuntimeException("Failed to send FCM message to topic", e);
        }
    }

    /**
     * Subscribe tokens to a topic
     */
    public void subscribeToTopic(List<String> fcmTokens, String topic) {
        try {
            TopicManagementResponse response = firebaseMessaging.subscribeToTopic(fcmTokens, topic);
            log.info("Successfully subscribed {} tokens to topic: {}", response.getSuccessCount(), topic);
            
            if (response.getFailureCount() > 0) {
                log.warn("Failed to subscribe {} tokens to topic: {}", response.getFailureCount(), topic);
            }
        } catch (FirebaseMessagingException e) {
            log.error("Error subscribing to topic: {}", topic, e);
            throw new RuntimeException("Failed to subscribe to topic", e);
        }
    }

    /**
     * Unsubscribe tokens from a topic
     */
    public void unsubscribeFromTopic(List<String> fcmTokens, String topic) {
        try {
            TopicManagementResponse response = firebaseMessaging.unsubscribeFromTopic(fcmTokens, topic);
            log.info("Successfully unsubscribed {} tokens from topic: {}", response.getSuccessCount(), topic);
            
            if (response.getFailureCount() > 0) {
                log.warn("Failed to unsubscribe {} tokens from topic: {}", response.getFailureCount(), topic);
            }
        } catch (FirebaseMessagingException e) {
            log.error("Error unsubscribing from topic: {}", topic, e);
            throw new RuntimeException("Failed to unsubscribe from topic", e);
        }
    }

    /**
     * Map notification priority to Android priority
     */
    private AndroidConfig.Priority mapPriority(NotificationPriority priority) {
        if (priority == null) {
            return AndroidConfig.Priority.NORMAL;
        }
        
        return switch (priority) {
            case HIGH -> AndroidConfig.Priority.HIGH;
            case LOW, NORMAL -> AndroidConfig.Priority.NORMAL;
        };
    }
}
