package com.bright.chat.service;

import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * Service for Firestore database operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FirestoreService {

    private final Firestore firestore;

    /**
     * Save a document to Firestore
     */
    public <T> String saveDocument(String collection, T document) {
        try {
            DocumentReference docRef = firestore.collection(collection).document();
            ApiFuture<WriteResult> result = docRef.set(document);
            result.get(); // Wait for the operation to complete
            log.info("Document saved with ID: {}", docRef.getId());
            return docRef.getId();
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error saving document to collection: {}", collection, e);
            throw new RuntimeException("Failed to save document", e);
        }
    }

    /**
     * Save a document with a specific ID
     */
    public <T> void saveDocument(String collection, String documentId, T document) {
        try {
            DocumentReference docRef = firestore.collection(collection).document(documentId);
            ApiFuture<WriteResult> result = docRef.set(document);
            result.get(); // Wait for the operation to complete
            log.info("Document saved with ID: {}", documentId);
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error saving document with ID {} to collection: {}", documentId, collection, e);
            throw new RuntimeException("Failed to save document", e);
        }
    }

    /**
     * Update a document in Firestore
     */
    public void updateDocument(String collection, String documentId, Map<String, Object> updates) {
        try {
            DocumentReference docRef = firestore.collection(collection).document(documentId);
            ApiFuture<WriteResult> result = docRef.update(updates);
            result.get(); // Wait for the operation to complete
            log.info("Document updated with ID: {}", documentId);
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error updating document with ID {} in collection: {}", documentId, collection, e);
            throw new RuntimeException("Failed to update document", e);
        }
    }

    /**
     * Get a document by ID
     */
    public <T> T getDocument(String collection, String documentId, Class<T> clazz) {
        try {
            DocumentReference docRef = firestore.collection(collection).document(documentId);
            ApiFuture<DocumentSnapshot> future = docRef.get();
            DocumentSnapshot document = future.get();
            
            if (document.exists()) {
                return document.toObject(clazz);
            } else {
                log.warn("Document with ID {} not found in collection: {}", documentId, collection);
                return null;
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error getting document with ID {} from collection: {}", documentId, collection, e);
            throw new RuntimeException("Failed to get document", e);
        }
    }

    /**
     * Get all documents from a collection
     */
    public <T> List<T> getAllDocuments(String collection, Class<T> clazz) {
        try {
            ApiFuture<QuerySnapshot> future = firestore.collection(collection).get();
            List<QueryDocumentSnapshot> documents = future.get().getDocuments();
            
            return documents.stream()
                    .map(doc -> doc.toObject(clazz))
                    .collect(Collectors.toList());
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error getting all documents from collection: {}", collection, e);
            throw new RuntimeException("Failed to get documents", e);
        }
    }

    /**
     * Query documents with conditions
     */
    public <T> List<T> queryDocuments(String collection, String field, Object value, Class<T> clazz) {
        try {
            ApiFuture<QuerySnapshot> future = firestore.collection(collection)
                    .whereEqualTo(field, value)
                    .get();
            List<QueryDocumentSnapshot> documents = future.get().getDocuments();
            
            return documents.stream()
                    .map(doc -> doc.toObject(clazz))
                    .collect(Collectors.toList());
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error querying documents from collection: {}", collection, e);
            throw new RuntimeException("Failed to query documents", e);
        }
    }

    /**
     * Delete a document
     */
    public void deleteDocument(String collection, String documentId) {
        try {
            ApiFuture<WriteResult> writeResult = firestore.collection(collection)
                    .document(documentId)
                    .delete();
            writeResult.get(); // Wait for the operation to complete
            log.info("Document deleted with ID: {}", documentId);
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error deleting document with ID {} from collection: {}", documentId, collection, e);
            throw new RuntimeException("Failed to delete document", e);
        }
    }

    /**
     * Get a subcollection reference
     */
    public CollectionReference getSubcollection(String collection, String documentId, String subcollection) {
        return firestore.collection(collection).document(documentId).collection(subcollection);
    }

    /**
     * Save document to subcollection
     */
    public <T> String saveToSubcollection(String collection, String documentId, String subcollection, T document) {
        try {
            CollectionReference subCollectionRef = getSubcollection(collection, documentId, subcollection);
            DocumentReference docRef = subCollectionRef.document();
            ApiFuture<WriteResult> result = docRef.set(document);
            result.get(); // Wait for the operation to complete
            log.info("Document saved to subcollection with ID: {}", docRef.getId());
            return docRef.getId();
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error saving document to subcollection: {}/{}/{}", collection, documentId, subcollection, e);
            throw new RuntimeException("Failed to save document to subcollection", e);
        }
    }

    /**
     * Get documents from subcollection
     */
    public <T> List<T> getFromSubcollection(String collection, String documentId, String subcollection, Class<T> clazz) {
        try {
            CollectionReference subCollectionRef = getSubcollection(collection, documentId, subcollection);
            ApiFuture<QuerySnapshot> future = subCollectionRef.get();
            List<QueryDocumentSnapshot> documents = future.get().getDocuments();
            
            return documents.stream()
                    .map(doc -> doc.toObject(clazz))
                    .collect(Collectors.toList());
        } catch (InterruptedException | ExecutionException e) {
            log.error("Error getting documents from subcollection: {}/{}/{}", collection, documentId, subcollection, e);
            throw new RuntimeException("Failed to get documents from subcollection", e);
        }
    }
}
