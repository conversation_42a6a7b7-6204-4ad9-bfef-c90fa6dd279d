package com.bright.chat.service;

import com.bright.chat.model.*;
import com.bright.chat.repository.GroupRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing groups
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GroupService {

    private final GroupRepository groupRepository;
    private final ConversationService conversationService;
    private final ChatUserService chatUserService;
    private final NotificationService notificationService;

    /**
     * Create a new group
     */
    public Group createGroup(String creatorId, String name, String description, 
                           GroupType type, GroupPrivacy privacy, List<String> memberIds) {
        // Validate creator exists
        if (!chatUserService.validateUser(creatorId)) {
            throw new RuntimeException("Creator not found: " + creatorId);
        }

        // Validate all members exist
        List<String> validMembers = chatUserService.validateUsers(memberIds);
        if (validMembers.size() != memberIds.size()) {
            throw new RuntimeException("Some members not found");
        }

        // Add creator to members if not already included
        if (!validMembers.contains(creatorId)) {
            validMembers.add(creatorId);
        }

        // Create associated conversation first
        Conversation conversation = conversationService.createGroupConversation(
                creatorId, name, description, validMembers);

        // Create group permissions
        GroupPermissions permissions = GroupPermissions.builder()
                .membersCanSendMessages(true)
                .membersCanSendMedia(true)
                .membersCanAddOthers(type == GroupType.PUBLIC)
                .isDiscoverable(privacy == GroupPrivacy.PUBLIC)
                .allowJoinByLink(type == GroupType.PUBLIC)
                .build();

        // Create group
        Group group = Group.builder()
                .name(name)
                .description(description)
                .type(type)
                .privacy(privacy)
                .isActive(true)
                .memberIds(validMembers)
                .ownerId(creatorId)
                .adminIds(List.of(creatorId))
                .maxMembers(500) // Default max members
                .memberCount(validMembers.size())
                .permissions(permissions)
                .conversationId(conversation.getId())
                .createdBy(creatorId)
                .lastActivity(LocalDateTime.now())
                .build();

        // Create group members
        for (String memberId : validMembers) {
            GroupMember member = GroupMember.builder()
                    .userId(memberId)
                    .role(memberId.equals(creatorId) ? GroupRole.OWNER : GroupRole.MEMBER)
                    .status(GroupMemberStatus.ACTIVE)
                    .joinedAt(LocalDateTime.now())
                    .canSendMessages(true)
                    .build();
            
            if (group.getMembers() == null) {
                group.setMembers(Map.of(memberId, member));
            } else {
                group.getMembers().put(memberId, member);
            }
        }

        // Save group
        String groupId = groupRepository.save(group);
        group.setId(groupId);

        // Add group to all members
        for (String memberId : validMembers) {
            chatUserService.addGroup(memberId, groupId);
        }

        // Send notifications to members (except creator)
        for (String memberId : validMembers) {
            if (!memberId.equals(creatorId)) {
                notificationService.sendGroupInviteNotification(memberId, conversation.getId(), creatorId);
            }
        }

        log.info("Created group {} with {} members", groupId, validMembers.size());
        return group;
    }

    /**
     * Get group by ID
     */
    public Optional<Group> getGroup(String groupId) {
        return groupRepository.findById(groupId);
    }

    /**
     * Get groups for a user
     */
    public List<Group> getUserGroups(String userId) {
        return groupRepository.findByMemberId(userId);
    }

    /**
     * Add member to group
     */
    public void addMember(String groupId, String userId, String addedBy) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        Group group = groupOpt.get();
        
        // Check if user is already a member
        if (group.getMemberIds().contains(userId)) {
            log.warn("User {} is already a member of group {}", userId, groupId);
            return;
        }

        // Check group capacity
        if (group.getMemberCount() >= group.getMaxMembers()) {
            throw new RuntimeException("Group has reached maximum capacity");
        }

        // Validate user exists
        if (!chatUserService.validateUser(userId)) {
            throw new RuntimeException("User not found: " + userId);
        }

        // Check if user is banned
        if (group.getBannedUserIds() != null && group.getBannedUserIds().contains(userId)) {
            throw new RuntimeException("User is banned from this group");
        }

        // Create group member
        GroupMember member = GroupMember.builder()
                .userId(userId)
                .role(GroupRole.MEMBER)
                .status(GroupMemberStatus.ACTIVE)
                .joinedAt(LocalDateTime.now())
                .invitedBy(addedBy)
                .canSendMessages(true)
                .build();

        // Add member to group
        groupRepository.addMember(groupId, userId, member);

        // Add member to conversation
        conversationService.addParticipant(group.getConversationId(), userId, addedBy);

        // Add group to user
        chatUserService.addGroup(userId, groupId);

        // Send notification
        notificationService.sendGroupInviteNotification(userId, group.getConversationId(), addedBy);

        log.info("Added member {} to group {}", userId, groupId);
    }

    /**
     * Remove member from group
     */
    public void removeMember(String groupId, String userId, String removedBy) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        Group group = groupOpt.get();
        
        // Check if user is a member
        if (!group.getMemberIds().contains(userId)) {
            log.warn("User {} is not a member of group {}", userId, groupId);
            return;
        }

        // Cannot remove group owner
        if (userId.equals(group.getOwnerId())) {
            throw new RuntimeException("Cannot remove group owner");
        }

        // Remove member from group
        groupRepository.removeMember(groupId, userId);

        // Remove member from conversation
        conversationService.removeParticipant(group.getConversationId(), userId, removedBy);

        // Remove group from user
        chatUserService.removeGroup(userId, groupId);

        log.info("Removed member {} from group {}", userId, groupId);
    }

    /**
     * Leave group
     */
    public void leaveGroup(String groupId, String userId) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        Group group = groupOpt.get();
        
        // If owner is leaving, transfer ownership or delete group
        if (userId.equals(group.getOwnerId())) {
            if (group.getAdminIds().size() > 1) {
                // Transfer ownership to another admin
                String newOwner = group.getAdminIds().stream()
                        .filter(adminId -> !adminId.equals(userId))
                        .findFirst()
                        .orElse(null);
                
                if (newOwner != null) {
                    transferOwnership(groupId, newOwner);
                }
            } else if (group.getMemberIds().size() > 1) {
                // Transfer ownership to any member
                String newOwner = group.getMemberIds().stream()
                        .filter(memberId -> !memberId.equals(userId))
                        .findFirst()
                        .orElse(null);
                
                if (newOwner != null) {
                    transferOwnership(groupId, newOwner);
                    promoteToAdmin(groupId, newOwner);
                }
            } else {
                // Delete group if no other members
                deleteGroup(groupId, userId);
                return;
            }
        }

        // Remove member
        removeMember(groupId, userId, userId);
        log.info("User {} left group {}", userId, groupId);
    }

    /**
     * Promote member to admin
     */
    public void promoteToAdmin(String groupId, String userId) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        Group group = groupOpt.get();
        
        // Check if user is a member
        if (!group.getMemberIds().contains(userId)) {
            throw new RuntimeException("User is not a member of this group");
        }

        // Add to admin list
        groupRepository.addAdmin(groupId, userId);

        // Update member role
        if (group.getMembers() != null && group.getMembers().containsKey(userId)) {
            GroupMember member = group.getMembers().get(userId);
            member.setRole(GroupRole.ADMIN);
            member.setCanAddMembers(true);
            member.setCanRemoveMembers(true);
            member.setCanEditGroup(true);
            groupRepository.save(group);
        }

        log.info("Promoted user {} to admin in group {}", userId, groupId);
    }

    /**
     * Demote admin to member
     */
    public void demoteAdmin(String groupId, String userId) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        Group group = groupOpt.get();
        
        // Cannot demote group owner
        if (userId.equals(group.getOwnerId())) {
            throw new RuntimeException("Cannot demote group owner");
        }

        // Remove from admin list
        groupRepository.removeAdmin(groupId, userId);

        // Update member role
        if (group.getMembers() != null && group.getMembers().containsKey(userId)) {
            GroupMember member = group.getMembers().get(userId);
            member.setRole(GroupRole.MEMBER);
            member.setCanAddMembers(false);
            member.setCanRemoveMembers(false);
            member.setCanEditGroup(false);
            groupRepository.save(group);
        }

        log.info("Demoted admin {} to member in group {}", userId, groupId);
    }

    /**
     * Transfer group ownership
     */
    public void transferOwnership(String groupId, String newOwnerId) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        Group group = groupOpt.get();
        
        // Check if new owner is a member
        if (!group.getMemberIds().contains(newOwnerId)) {
            throw new RuntimeException("New owner must be a group member");
        }

        // Update ownership
        group.setOwnerId(newOwnerId);
        
        // Make new owner an admin if not already
        if (!group.getAdminIds().contains(newOwnerId)) {
            group.getAdminIds().add(newOwnerId);
        }

        // Update member role
        if (group.getMembers() != null && group.getMembers().containsKey(newOwnerId)) {
            GroupMember member = group.getMembers().get(newOwnerId);
            member.setRole(GroupRole.OWNER);
        }

        groupRepository.save(group);
        log.info("Transferred ownership of group {} to user {}", groupId, newOwnerId);
    }

    /**
     * Update group information
     */
    public void updateGroupInfo(String groupId, String name, String description, String updatedBy) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        groupRepository.updateInfo(groupId, name, description);
        
        // Also update conversation name
        Group group = groupOpt.get();
        conversationService.updateConversationName(group.getConversationId(), name, updatedBy);
        
        log.info("Updated group {} info", groupId);
    }

    /**
     * Generate invite link
     */
    public String generateInviteLink(String groupId, int maxUses, int validDays) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        Group group = groupOpt.get();
        
        if (!group.getPermissions().isAllowJoinByLink()) {
            throw new RuntimeException("Group does not allow join by link");
        }

        String inviteCode = UUID.randomUUID().toString().substring(0, 8);
        LocalDateTime expiry = LocalDateTime.now().plusDays(validDays);
        
        groupRepository.updateInviteCode(groupId, inviteCode, expiry, maxUses);
        
        log.info("Generated invite link for group {}: {}", groupId, inviteCode);
        return inviteCode;
    }

    /**
     * Join group by invite code
     */
    public void joinByInviteCode(String inviteCode, String userId) {
        // Find group by invite code
        List<Group> allGroups = groupRepository.findByType(GroupType.PUBLIC);
        Group group = allGroups.stream()
                .filter(g -> inviteCode.equals(g.getInviteCode()))
                .filter(g -> g.getInviteCodeExpiry() == null || g.getInviteCodeExpiry().isAfter(LocalDateTime.now()))
                .filter(g -> g.getCurrentInviteUses() < g.getMaxInviteUses())
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Invalid or expired invite code"));

        // Add member to group
        addMember(group.getId(), userId, "invite_link");
        
        // Increment invite usage
        groupRepository.incrementInviteUse(group.getId());
        
        log.info("User {} joined group {} via invite code", userId, group.getId());
    }

    /**
     * Ban user from group
     */
    public void banUser(String groupId, String userId, String bannedBy) {
        groupRepository.banUser(groupId, userId);
        log.info("Banned user {} from group {} by {}", userId, groupId, bannedBy);
    }

    /**
     * Unban user from group
     */
    public void unbanUser(String groupId, String userId, String unbannedBy) {
        groupRepository.unbanUser(groupId, userId);
        log.info("Unbanned user {} from group {} by {}", userId, groupId, unbannedBy);
    }

    /**
     * Mute user in group
     */
    public void muteUser(String groupId, String userId, int durationMinutes, String mutedBy) {
        LocalDateTime muteUntil = LocalDateTime.now().plusMinutes(durationMinutes);
        groupRepository.muteUser(groupId, userId, muteUntil);
        log.info("Muted user {} in group {} until {} by {}", userId, groupId, muteUntil, mutedBy);
    }

    /**
     * Unmute user in group
     */
    public void unmuteUser(String groupId, String userId, String unmutedBy) {
        groupRepository.unmuteUser(groupId, userId);
        log.info("Unmuted user {} in group {} by {}", userId, groupId, unmutedBy);
    }

    /**
     * Delete group
     */
    public void deleteGroup(String groupId, String deletedBy) {
        Optional<Group> groupOpt = groupRepository.findById(groupId);
        if (groupOpt.isEmpty()) {
            throw new RuntimeException("Group not found: " + groupId);
        }

        Group group = groupOpt.get();
        
        // Remove group from all members
        for (String memberId : group.getMemberIds()) {
            chatUserService.removeGroup(memberId, groupId);
        }

        // Delete associated conversation
        conversationService.deleteConversation(group.getConversationId(), deletedBy);

        // Delete group
        groupRepository.delete(groupId);
        log.info("Deleted group {} by user {}", groupId, deletedBy);
    }

    /**
     * Search public groups
     */
    public List<Group> searchPublicGroups(String query) {
        return groupRepository.findPublicGroups().stream()
                .filter(group -> group.getName().toLowerCase().contains(query.toLowerCase()) ||
                               (group.getDescription() != null && 
                                group.getDescription().toLowerCase().contains(query.toLowerCase())))
                .toList();
    }
}
