package com.bright.chat.service;

import com.bright.chat.model.*;
import com.bright.chat.repository.MessageRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service for managing messages
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MessageService {

    private final MessageRepository messageRepository;
    private final ConversationService conversationService;
    private final ChatUserService chatUserService;
    private final NotificationService notificationService;
    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Send a text message
     */
    public Message sendTextMessage(String conversationId, String senderId, String text) {
        return sendMessage(conversationId, senderId, text, MessageType.TEXT, null);
    }

    /**
     * Send a message with attachments
     */
    public Message sendMessageWithAttachments(String conversationId, String senderId, String text, 
                                            MessageType type, List<MessageAttachment> attachments) {
        return sendMessage(conversationId, senderId, text, type, attachments);
    }

    /**
     * Send a message (generic method)
     */
    private Message sendMessage(String conversationId, String senderId, String text, 
                               MessageType type, List<MessageAttachment> attachments) {
        // Validate conversation exists and user is participant
        if (!conversationService.isParticipant(conversationId, senderId)) {
            throw new RuntimeException("User is not a participant in this conversation");
        }

        // Get sender info
        Optional<ChatUser> senderOpt = chatUserService.getChatUser(senderId);
        if (senderOpt.isEmpty()) {
            throw new RuntimeException("Sender not found: " + senderId);
        }

        ChatUser sender = senderOpt.get();

        // Create message
        Message message = Message.builder()
                .conversationId(conversationId)
                .senderId(senderId)
                .senderName(sender.getDisplayName())
                .senderAvatarUrl(sender.getAvatarUrl())
                .text(text)
                .type(type)
                .attachments(attachments)
                .status(MessageStatus.SENT)
                .priority(MessagePriority.NORMAL)
                .build();

        // Save message
        String messageId = messageRepository.save(message);
        message.setId(messageId);

        // Update conversation last message
        conversationService.updateLastMessage(conversationId, messageId, text, senderId, type);

        // Send real-time notification via WebSocket
        sendRealTimeMessage(conversationId, message);

        // Send push notifications to other participants
        sendPushNotifications(conversationId, senderId, message);

        log.info("Sent message {} in conversation {}", messageId, conversationId);
        return message;
    }

    /**
     * Reply to a message
     */
    public Message replyToMessage(String conversationId, String senderId, String text, String replyToMessageId) {
        // Validate original message exists
        Optional<Message> originalMessageOpt = messageRepository.findById(conversationId, replyToMessageId);
        if (originalMessageOpt.isEmpty()) {
            throw new RuntimeException("Original message not found: " + replyToMessageId);
        }

        // Get sender info
        Optional<ChatUser> senderOpt = chatUserService.getChatUser(senderId);
        if (senderOpt.isEmpty()) {
            throw new RuntimeException("Sender not found: " + senderId);
        }

        ChatUser sender = senderOpt.get();

        // Create reply message
        Message message = Message.builder()
                .conversationId(conversationId)
                .senderId(senderId)
                .senderName(sender.getDisplayName())
                .senderAvatarUrl(sender.getAvatarUrl())
                .text(text)
                .type(MessageType.TEXT)
                .replyToMessageId(replyToMessageId)
                .status(MessageStatus.SENT)
                .priority(MessagePriority.NORMAL)
                .build();

        // Save message
        String messageId = messageRepository.save(message);
        message.setId(messageId);

        // Update conversation last message
        conversationService.updateLastMessage(conversationId, messageId, text, senderId, MessageType.TEXT);

        // Send real-time notification
        sendRealTimeMessage(conversationId, message);

        // Send push notifications
        sendPushNotifications(conversationId, senderId, message);

        log.info("Sent reply message {} to message {} in conversation {}", messageId, replyToMessageId, conversationId);
        return message;
    }

    /**
     * Edit a message
     */
    public void editMessage(String conversationId, String messageId, String newText, String userId) {
        // Validate message exists and user is the sender
        Optional<Message> messageOpt = messageRepository.findById(conversationId, messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("Message not found: " + messageId);
        }

        Message message = messageOpt.get();
        if (!message.getSenderId().equals(userId)) {
            throw new RuntimeException("User can only edit their own messages");
        }

        // Update message
        messageRepository.updateText(conversationId, messageId, newText);

        // Send real-time update
        messagingTemplate.convertAndSend("/topic/conversation/" + conversationId + "/edit", 
                Map.of("messageId", messageId, "newText", newText, "editedAt", LocalDateTime.now()));

        log.info("Edited message {} in conversation {}", messageId, conversationId);
    }

    /**
     * Delete a message
     */
    public void deleteMessage(String conversationId, String messageId, String userId) {
        // Validate message exists and user is the sender
        Optional<Message> messageOpt = messageRepository.findById(conversationId, messageId);
        if (messageOpt.isEmpty()) {
            throw new RuntimeException("Message not found: " + messageId);
        }

        Message message = messageOpt.get();
        if (!message.getSenderId().equals(userId)) {
            throw new RuntimeException("User can only delete their own messages");
        }

        // Mark as deleted
        messageRepository.markAsDeleted(conversationId, messageId, userId);

        // Send real-time update
        messagingTemplate.convertAndSend("/topic/conversation/" + conversationId + "/delete", 
                Map.of("messageId", messageId, "deletedBy", userId, "deletedAt", LocalDateTime.now()));

        log.info("Deleted message {} in conversation {}", messageId, conversationId);
    }

    /**
     * Mark message as read
     */
    public void markMessageAsRead(String conversationId, String messageId, String userId) {
        messageRepository.markAsRead(conversationId, messageId, userId);

        // Send real-time read receipt
        messagingTemplate.convertAndSend("/topic/conversation/" + conversationId + "/read", 
                Map.of("messageId", messageId, "userId", userId, "readAt", LocalDateTime.now()));

        log.info("Marked message {} as read by user {} in conversation {}", messageId, userId, conversationId);
    }

    /**
     * Add reaction to message
     */
    public void addReaction(String conversationId, String messageId, String emoji, String userId) {
        messageRepository.addReaction(conversationId, messageId, emoji, userId);

        // Send real-time reaction update
        messagingTemplate.convertAndSend("/topic/conversation/" + conversationId + "/reaction", 
                Map.of("messageId", messageId, "emoji", emoji, "userId", userId, "action", "add"));

        log.info("Added reaction {} to message {} by user {} in conversation {}", emoji, messageId, userId, conversationId);
    }

    /**
     * Remove reaction from message
     */
    public void removeReaction(String conversationId, String messageId, String emoji, String userId) {
        messageRepository.removeReaction(conversationId, messageId, emoji, userId);

        // Send real-time reaction update
        messagingTemplate.convertAndSend("/topic/conversation/" + conversationId + "/reaction", 
                Map.of("messageId", messageId, "emoji", emoji, "userId", userId, "action", "remove"));

        log.info("Removed reaction {} from message {} by user {} in conversation {}", emoji, messageId, userId, conversationId);
    }

    /**
     * Get messages in conversation
     */
    public List<Message> getMessages(String conversationId, String userId) {
        // Validate user is participant
        if (!conversationService.isParticipant(conversationId, userId)) {
            throw new RuntimeException("User is not a participant in this conversation");
        }

        return messageRepository.findByConversationId(conversationId);
    }

    /**
     * Get recent messages with pagination
     */
    public List<Message> getRecentMessages(String conversationId, String userId, int limit) {
        // Validate user is participant
        if (!conversationService.isParticipant(conversationId, userId)) {
            throw new RuntimeException("User is not a participant in this conversation");
        }

        return messageRepository.findRecentMessages(conversationId, limit);
    }

    /**
     * Send real-time message via WebSocket
     */
    private void sendRealTimeMessage(String conversationId, Message message) {
        messagingTemplate.convertAndSend("/topic/conversation/" + conversationId, message);
        log.debug("Sent real-time message to conversation: {}", conversationId);
    }

    /**
     * Send push notifications to conversation participants
     */
    private void sendPushNotifications(String conversationId, String senderId, Message message) {
        List<String> participants = conversationService.getParticipants(conversationId);
        
        for (String participantId : participants) {
            // Don't send notification to sender
            if (!participantId.equals(senderId)) {
                // Check if user allows notifications and is not blocking sender
                Optional<ChatUser> participantOpt = chatUserService.getChatUser(participantId);
                if (participantOpt.isPresent()) {
                    ChatUser participant = participantOpt.get();
                    if (participant.isAllowNotifications() && 
                        !chatUserService.isUserBlocked(participantId, senderId)) {
                        
                        notificationService.sendNewMessageNotification(participantId, conversationId, message);
                    }
                }
            }
        }
    }

    /**
     * Forward message to another conversation
     */
    public Message forwardMessage(String originalConversationId, String originalMessageId, 
                                 String targetConversationId, String forwardedBy) {
        // Get original message
        Optional<Message> originalMessageOpt = messageRepository.findById(originalConversationId, originalMessageId);
        if (originalMessageOpt.isEmpty()) {
            throw new RuntimeException("Original message not found");
        }

        Message originalMessage = originalMessageOpt.get();
        
        // Get forwarder info
        Optional<ChatUser> forwarderOpt = chatUserService.getChatUser(forwardedBy);
        if (forwarderOpt.isEmpty()) {
            throw new RuntimeException("Forwarder not found: " + forwardedBy);
        }

        ChatUser forwarder = forwarderOpt.get();

        // Create forwarded message
        Message forwardedMessage = Message.builder()
                .conversationId(targetConversationId)
                .senderId(forwardedBy)
                .senderName(forwarder.getDisplayName())
                .senderAvatarUrl(forwarder.getAvatarUrl())
                .text(originalMessage.getText())
                .type(originalMessage.getType())
                .attachments(originalMessage.getAttachments())
                .isForwarded(true)
                .originalMessageId(originalMessageId)
                .originalConversationId(originalConversationId)
                .status(MessageStatus.SENT)
                .priority(MessagePriority.NORMAL)
                .build();

        // Save forwarded message
        String messageId = messageRepository.save(forwardedMessage);
        forwardedMessage.setId(messageId);

        // Update conversation last message
        conversationService.updateLastMessage(targetConversationId, messageId, 
                "Forwarded: " + originalMessage.getText(), forwardedBy, originalMessage.getType());

        // Send real-time notification
        sendRealTimeMessage(targetConversationId, forwardedMessage);

        // Send push notifications
        sendPushNotifications(targetConversationId, forwardedBy, forwardedMessage);

        log.info("Forwarded message {} from conversation {} to conversation {}", 
                originalMessageId, originalConversationId, targetConversationId);
        
        return forwardedMessage;
    }
}
