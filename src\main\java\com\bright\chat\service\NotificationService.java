package com.bright.chat.service;

import com.bright.chat.model.*;
import com.bright.chat.repository.NotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service for managing notifications and FCM push notifications
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {

    private final NotificationRepository notificationRepository;
    private final FCMService fcmService;
    private final ChatUserService chatUserService;

    /**
     * Send new message notification
     */
    public void sendNewMessageNotification(String userId, String conversationId, Message message) {
        Optional<ChatUser> userOpt = chatUserService.getChatUser(userId);
        if (userOpt.isEmpty() || userOpt.get().getFcmToken() == null) {
            log.warn("Cannot send notification - user not found or no FCM token: {}", userId);
            return;
        }

        ChatUser user = userOpt.get();
        
        // Create notification
        Notification notification = Notification.builder()
                .userId(userId)
                .senderId(message.getSenderId())
                .title(message.getSenderName())
                .body(getMessagePreview(message))
                .type(NotificationType.NEW_MESSAGE)
                .conversationId(conversationId)
                .messageId(message.getId())
                .fcmToken(user.getFcmToken())
                .priority(NotificationPriority.NORMAL)
                .data(Map.of(
                        "conversationId", conversationId,
                        "messageId", message.getId(),
                        "senderId", message.getSenderId(),
                        "messageType", message.getType().toString()
                ))
                .build();

        // Save notification
        String notificationId = notificationRepository.save(notification);
        notification.setId(notificationId);

        // Send FCM notification
        try {
            String fcmMessageId = fcmService.sendNotification(notification);
            notificationRepository.updateFcmMessageId(notificationId, fcmMessageId);
            log.info("Sent new message notification to user: {}", userId);
        } catch (Exception e) {
            notificationRepository.markAsFailed(notificationId, e.getMessage());
            log.error("Failed to send new message notification to user: {}", userId, e);
        }
    }

    /**
     * Send group invite notification
     */
    public void sendGroupInviteNotification(String userId, String conversationId, String invitedBy) {
        Optional<ChatUser> userOpt = chatUserService.getChatUser(userId);
        Optional<ChatUser> inviterOpt = chatUserService.getChatUser(invitedBy);
        
        if (userOpt.isEmpty() || userOpt.get().getFcmToken() == null) {
            log.warn("Cannot send notification - user not found or no FCM token: {}", userId);
            return;
        }

        ChatUser user = userOpt.get();
        String inviterName = inviterOpt.map(ChatUser::getDisplayName).orElse("Someone");

        // Create notification
        Notification notification = Notification.builder()
                .userId(userId)
                .senderId(invitedBy)
                .title("Group Invitation")
                .body(inviterName + " added you to a group")
                .type(NotificationType.GROUP_INVITE)
                .conversationId(conversationId)
                .fcmToken(user.getFcmToken())
                .priority(NotificationPriority.HIGH)
                .data(Map.of(
                        "conversationId", conversationId,
                        "invitedBy", invitedBy,
                        "inviterName", inviterName
                ))
                .build();

        // Save and send notification
        String notificationId = notificationRepository.save(notification);
        notification.setId(notificationId);

        try {
            String fcmMessageId = fcmService.sendNotification(notification);
            notificationRepository.updateFcmMessageId(notificationId, fcmMessageId);
            log.info("Sent group invite notification to user: {}", userId);
        } catch (Exception e) {
            notificationRepository.markAsFailed(notificationId, e.getMessage());
            log.error("Failed to send group invite notification to user: {}", userId, e);
        }
    }

    /**
     * Send group message notification
     */
    public void sendGroupMessageNotification(String userId, String groupId, Message message) {
        Optional<ChatUser> userOpt = chatUserService.getChatUser(userId);
        if (userOpt.isEmpty() || userOpt.get().getFcmToken() == null) {
            log.warn("Cannot send notification - user not found or no FCM token: {}", userId);
            return;
        }

        ChatUser user = userOpt.get();

        // Create notification
        Notification notification = Notification.builder()
                .userId(userId)
                .senderId(message.getSenderId())
                .title("Group Message")
                .body(message.getSenderName() + ": " + getMessagePreview(message))
                .type(NotificationType.NEW_GROUP_MESSAGE)
                .conversationId(message.getConversationId())
                .messageId(message.getId())
                .groupId(groupId)
                .fcmToken(user.getFcmToken())
                .priority(NotificationPriority.NORMAL)
                .data(Map.of(
                        "conversationId", message.getConversationId(),
                        "groupId", groupId,
                        "messageId", message.getId(),
                        "senderId", message.getSenderId(),
                        "messageType", message.getType().toString()
                ))
                .build();

        // Save and send notification
        String notificationId = notificationRepository.save(notification);
        notification.setId(notificationId);

        try {
            String fcmMessageId = fcmService.sendNotification(notification);
            notificationRepository.updateFcmMessageId(notificationId, fcmMessageId);
            log.info("Sent group message notification to user: {}", userId);
        } catch (Exception e) {
            notificationRepository.markAsFailed(notificationId, e.getMessage());
            log.error("Failed to send group message notification to user: {}", userId, e);
        }
    }

    /**
     * Send mention notification
     */
    public void sendMentionNotification(String userId, String conversationId, Message message) {
        Optional<ChatUser> userOpt = chatUserService.getChatUser(userId);
        if (userOpt.isEmpty() || userOpt.get().getFcmToken() == null) {
            log.warn("Cannot send notification - user not found or no FCM token: {}", userId);
            return;
        }

        ChatUser user = userOpt.get();

        // Create notification
        Notification notification = Notification.builder()
                .userId(userId)
                .senderId(message.getSenderId())
                .title("You were mentioned")
                .body(message.getSenderName() + " mentioned you: " + getMessagePreview(message))
                .type(NotificationType.MENTION)
                .conversationId(conversationId)
                .messageId(message.getId())
                .fcmToken(user.getFcmToken())
                .priority(NotificationPriority.HIGH)
                .data(Map.of(
                        "conversationId", conversationId,
                        "messageId", message.getId(),
                        "senderId", message.getSenderId(),
                        "messageType", message.getType().toString()
                ))
                .build();

        // Save and send notification
        String notificationId = notificationRepository.save(notification);
        notification.setId(notificationId);

        try {
            String fcmMessageId = fcmService.sendNotification(notification);
            notificationRepository.updateFcmMessageId(notificationId, fcmMessageId);
            log.info("Sent mention notification to user: {}", userId);
        } catch (Exception e) {
            notificationRepository.markAsFailed(notificationId, e.getMessage());
            log.error("Failed to send mention notification to user: {}", userId, e);
        }
    }

    /**
     * Send message reaction notification
     */
    public void sendReactionNotification(String userId, String conversationId, String messageId, 
                                       String emoji, String reactedBy) {
        Optional<ChatUser> userOpt = chatUserService.getChatUser(userId);
        Optional<ChatUser> reactorOpt = chatUserService.getChatUser(reactedBy);
        
        if (userOpt.isEmpty() || userOpt.get().getFcmToken() == null) {
            log.warn("Cannot send notification - user not found or no FCM token: {}", userId);
            return;
        }

        ChatUser user = userOpt.get();
        String reactorName = reactorOpt.map(ChatUser::getDisplayName).orElse("Someone");

        // Create notification
        Notification notification = Notification.builder()
                .userId(userId)
                .senderId(reactedBy)
                .title("Message Reaction")
                .body(reactorName + " reacted " + emoji + " to your message")
                .type(NotificationType.MESSAGE_REACTION)
                .conversationId(conversationId)
                .messageId(messageId)
                .fcmToken(user.getFcmToken())
                .priority(NotificationPriority.LOW)
                .data(Map.of(
                        "conversationId", conversationId,
                        "messageId", messageId,
                        "reactedBy", reactedBy,
                        "emoji", emoji
                ))
                .build();

        // Save and send notification
        String notificationId = notificationRepository.save(notification);
        notification.setId(notificationId);

        try {
            String fcmMessageId = fcmService.sendNotification(notification);
            notificationRepository.updateFcmMessageId(notificationId, fcmMessageId);
            log.info("Sent reaction notification to user: {}", userId);
        } catch (Exception e) {
            notificationRepository.markAsFailed(notificationId, e.getMessage());
            log.error("Failed to send reaction notification to user: {}", userId, e);
        }
    }

    /**
     * Get user notifications
     */
    public List<Notification> getUserNotifications(String userId) {
        return notificationRepository.findByUserId(userId);
    }

    /**
     * Get unread notifications for user
     */
    public List<Notification> getUnreadNotifications(String userId) {
        return notificationRepository.findUnreadByUserId(userId);
    }

    /**
     * Mark notification as read
     */
    public void markNotificationAsRead(String notificationId) {
        notificationRepository.markAsRead(notificationId);
        log.info("Marked notification as read: {}", notificationId);
    }

    /**
     * Mark all notifications as read for user
     */
    public void markAllNotificationsAsRead(String userId) {
        List<Notification> unreadNotifications = getUnreadNotifications(userId);
        for (Notification notification : unreadNotifications) {
            notificationRepository.markAsRead(notification.getId());
        }
        log.info("Marked all notifications as read for user: {}", userId);
    }

    /**
     * Retry failed notifications
     */
    public void retryFailedNotifications() {
        List<Notification> failedNotifications = notificationRepository.findFailedForRetry();
        
        for (Notification notification : failedNotifications) {
            try {
                String fcmMessageId = fcmService.sendNotification(notification);
                notificationRepository.updateFcmMessageId(notification.getId(), fcmMessageId);
                log.info("Retried notification successfully: {}", notification.getId());
            } catch (Exception e) {
                notificationRepository.markAsFailed(notification.getId(), e.getMessage());
                log.error("Failed to retry notification: {}", notification.getId(), e);
            }
        }
    }

    /**
     * Get message preview for notification
     */
    private String getMessagePreview(Message message) {
        switch (message.getType()) {
            case TEXT -> {
                return message.getText().length() > 50 ? 
                       message.getText().substring(0, 50) + "..." : 
                       message.getText();
            }
            case IMAGE -> {
                return "📷 Image";
            }
            case VIDEO -> {
                return "🎥 Video";
            }
            case AUDIO, VOICE_NOTE -> {
                return "🎵 Audio";
            }
            case FILE -> {
                return "📎 File";
            }
            case LOCATION -> {
                return "📍 Location";
            }
            case STICKER -> {
                return "😊 Sticker";
            }
            case GIF -> {
                return "🎬 GIF";
            }
            default -> {
                return "Message";
            }
        }
    }

    /**
     * Clean up old notifications
     */
    public void cleanupOldNotifications() {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(30); // Keep notifications for 30 days
        notificationRepository.deleteOldNotifications(cutoffDate);
        log.info("Cleaned up old notifications before: {}", cutoffDate);
    }
}
