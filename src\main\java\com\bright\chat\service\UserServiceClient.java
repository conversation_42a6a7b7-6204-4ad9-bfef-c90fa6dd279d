package com.bright.chat.service;

import com.bright.chat.model.UserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Optional;

/**
 * Client service for communicating with User Service
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserServiceClient {

    private final RestTemplate restTemplate;

    @Value("${user-service.base-url:http://localhost:8081}")
    private String userServiceBaseUrl;

    /**
     * Get user information by user ID with Redis caching
     */
    @Cacheable(value = "users", key = "#userId", unless = "#result.isEmpty()")
    public Optional<UserInfo> getUserById(String userId) {
        try {
            log.debug("Fetching user from User Service: {}", userId);
            String url = userServiceBaseUrl + "/api/users/" + userId;
            ResponseEntity<UserInfo> response = restTemplate.getForEntity(url, UserInfo.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("User found and cached: {}", userId);
                return Optional.of(response.getBody());
            }

            log.warn("User not found with ID: {}", userId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Error fetching user with ID: {}", userId, e);
            return Optional.empty();
        }
    }

    /**
     * Get multiple users by their IDs with caching
     */
    @Cacheable(value = "usersBatch", key = "#userIds.hashCode()", unless = "#result.isEmpty()")
    public List<UserInfo> getUsersByIds(List<String> userIds) {
        try {
            log.debug("Fetching {} users from User Service", userIds.size());
            String url = userServiceBaseUrl + "/api/users/batch";

            ResponseEntity<List<UserInfo>> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                null,
                new ParameterizedTypeReference<List<UserInfo>>() {},
                userIds
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Fetched and cached {} users", response.getBody().size());
                return response.getBody();
            }

            log.warn("Failed to fetch users for IDs: {}", userIds);
            return List.of();
        } catch (Exception e) {
            log.error("Error fetching users with IDs: {}", userIds, e);
            return List.of();
        }
    }

    /**
     * Search users by username or display name with caching
     */
    @Cacheable(value = "userSearch", key = "#query + '_' + #limit", unless = "#result.isEmpty()")
    public List<UserInfo> searchUsers(String query, int limit) {
        try {
            log.debug("Searching users with query: {} (limit: {})", query, limit);
            String url = userServiceBaseUrl + "/api/users/search?q=" + query + "&limit=" + limit;

            ResponseEntity<List<UserInfo>> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<List<UserInfo>>() {}
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Found and cached {} users for query: {}", response.getBody().size(), query);
                return response.getBody();
            }

            return List.of();
        } catch (Exception e) {
            log.error("Error searching users with query: {}", query, e);
            return List.of();
        }
    }

    /**
     * Check if user exists with caching
     */
    @Cacheable(value = "userValidation", key = "'exists_' + #userId")
    public boolean userExists(String userId) {
        try {
            log.debug("Checking if user exists: {}", userId);
            String url = userServiceBaseUrl + "/api/users/" + userId + "/exists";
            ResponseEntity<Boolean> response = restTemplate.getForEntity(url, Boolean.class);

            boolean exists = response.getStatusCode().is2xxSuccessful() &&
                           response.getBody() != null &&
                           response.getBody();

            log.debug("User {} exists: {}", userId, exists);
            return exists;
        } catch (Exception e) {
            log.error("Error checking if user exists with ID: {}", userId, e);
            return false;
        }
    }

    /**
     * Validate multiple user IDs with caching
     */
    @Cacheable(value = "userValidation", key = "'validate_' + #userIds.hashCode()")
    public List<String> validateUserIds(List<String> userIds) {
        try {
            log.debug("Validating {} user IDs", userIds.size());
            String url = userServiceBaseUrl + "/api/users/validate";

            ResponseEntity<List<String>> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                null,
                new ParameterizedTypeReference<List<String>>() {},
                userIds
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.debug("Validated {} out of {} user IDs", response.getBody().size(), userIds.size());
                return response.getBody();
            }

            return List.of();
        } catch (Exception e) {
            log.error("Error validating user IDs: {}", userIds, e);
            return List.of();
        }
    }

    /**
     * Clear user cache when user data is updated
     */
    @CacheEvict(value = "users", key = "#userId")
    public void evictUserCache(String userId) {
        log.debug("Evicted user cache for: {}", userId);
    }

    /**
     * Clear all user caches
     */
    @CacheEvict(value = {"users", "usersBatch", "userSearch", "userValidation"}, allEntries = true)
    public void evictAllUserCaches() {
        log.info("Evicted all user caches");
    }
}
