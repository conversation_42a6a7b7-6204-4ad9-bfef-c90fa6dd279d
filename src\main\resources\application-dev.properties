# Development Environment Configuration

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Logging Configuration (More verbose for development)
logging.level.com.bright.chat=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.com.google.firebase=INFO
logging.level.com.google.cloud=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# CORS Configuration (Permissive for development)
cors.allowed-origins=http://localhost:3000,http://localhost:3001,http://localhost:4200,http://localhost:8080,http://localhost:8081
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH,HEAD
cors.allowed-headers=*
cors.exposed-headers=X-Total-Count,X-Page-Count,X-User-Id,Authorization,Content-Disposition
cors.allow-credentials=true
cors.max-age=3600

# Firebase Configuration (Development)
firebase.service-account-key=firebase/serviceAccountKey-dev.json
firebase.project-id=bright-chat-dev
firebase.database-url=https://bright-chat-dev-default-rtdb.firebaseio.com/

# User Service Configuration
user-service.base-url=http://localhost:8081
user-service.timeout.connect=5000
user-service.timeout.read=10000

# WebSocket Configuration
spring.websocket.allowed-origins=http://localhost:3000,http://localhost:3001,http://localhost:4200

# Development specific settings
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Swagger Configuration (Enabled in development)
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html

# Redis Configuration (Development)
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=
spring.redis.database=0
spring.redis.timeout=2000
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

# Cache Configuration (Development - shorter TTL for testing)
spring.cache.type=redis
spring.cache.redis.time-to-live=600000
spring.cache.redis.cache-null-values=false
