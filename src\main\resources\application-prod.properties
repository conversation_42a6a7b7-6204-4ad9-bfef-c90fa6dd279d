# Production Environment Configuration

# Server Configuration
server.port=8080
server.servlet.context-path=/
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# Logging Configuration (Less verbose for production)
logging.level.com.bright.chat=INFO
logging.level.org.springframework.web=WARN
logging.level.org.springframework.security=WARN
logging.level.com.google.firebase=WARN
logging.level.com.google.cloud=WARN
logging.level.org.springframework.messaging=WARN
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/bright-chat.log
logging.file.max-size=10MB
logging.file.max-history=30

# CORS Configuration (Restrictive for production)
cors.allowed-origins=https://bright-chat.com,https://www.bright-chat.com,https://app.bright-chat.com,https://chat.bright-chat.com
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
cors.allowed-headers=Content-Type,Authorization,X-User-Id,X-Requested-With,Accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
cors.exposed-headers=X-Total-Count,X-Page-Count,X-User-Id
cors.allow-credentials=true
cors.max-age=86400

# Firebase Configuration (Production)
firebase.service-account-key=firebase/serviceAccountKey-prod.json
firebase.project-id=bright-chat-prod
firebase.database-url=https://bright-chat-prod-default-rtdb.firebaseio.com/

# User Service Configuration
user-service.base-url=https://user-service.bright-chat.com
user-service.timeout.connect=3000
user-service.timeout.read=8000

# WebSocket Configuration
spring.websocket.allowed-origins=https://bright-chat.com,https://www.bright-chat.com,https://app.bright-chat.com

# Security Configuration
server.ssl.enabled=false
server.forward-headers-strategy=native

# Performance Configuration
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Swagger Configuration (Disabled in production for security)
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false

# Actuator Configuration (Restricted in production)
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.endpoints.web.base-path=/actuator
management.security.enabled=true

# Redis Configuration (Production)
spring.redis.host=${REDIS_HOST:localhost}
spring.redis.port=${REDIS_PORT:6379}
spring.redis.password=${REDIS_PASSWORD:}
spring.redis.database=${REDIS_DATABASE:0}
spring.redis.timeout=3000
spring.redis.lettuce.pool.max-active=20
spring.redis.lettuce.pool.max-idle=10
spring.redis.lettuce.pool.min-idle=5
spring.redis.lettuce.pool.max-wait=2000ms

# Cache Configuration (Production - longer TTL)
spring.cache.type=redis
spring.cache.redis.time-to-live=3600000
spring.cache.redis.cache-null-values=false
