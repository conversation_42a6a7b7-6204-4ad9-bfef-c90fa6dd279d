# Test Environment Configuration

# Server Configuration
server.port=0
server.servlet.context-path=/

# Logging Configuration (Minimal for tests)
logging.level.com.bright.chat=WARN
logging.level.org.springframework.web=WARN
logging.level.org.springframework.security=WARN
logging.level.com.google.firebase=ERROR
logging.level.com.google.cloud=ERROR
logging.level.org.springframework.messaging=WARN

# CORS Configuration (Permissive for testing)
cors.allowed-origins=*
cors.allowed-methods=*
cors.allowed-headers=*
cors.exposed-headers=*
cors.allow-credentials=true
cors.max-age=3600

# Firebase Configuration (Disabled for tests)
firebase.enabled=false
firebase.service-account-key=firebase/serviceAccountKey-test.json
firebase.project-id=bright-chat-test
firebase.database-url=https://bright-chat-test-default-rtdb.firebaseio.com/

# User Service Configuration (Mock/Test)
user-service.base-url=http://localhost:8082
user-service.timeout.connect=1000
user-service.timeout.read=3000

# WebSocket Configuration
spring.websocket.allowed-origins=*

# Test specific settings
spring.test.database.replace=none
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false

# Swagger Configuration (Disabled in tests)
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false

# Disable security, Redis and Firebase for tests
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

# Disable cache for tests
spring.cache.type=none

# Mock Redis configuration (not used but prevents errors)
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=1000
