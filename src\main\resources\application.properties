# ===============================================
# BRIGHT CHAT APPLICATION CONFIGURATION
# ===============================================

# Application Basic Configuration
spring.application.name=chat
spring.profiles.active=${SPRING_PROFILES_ACTIVE:default}
spring.main.lazy-initialization=false

# ===============================================
# SERVER CONFIGURATION
# ===============================================
server.port=${PORT:8080}
server.servlet.context-path=/

# Production optimizations (enabled via profile)
server.compression.enabled=${SERVER_COMPRESSION_ENABLED:false}
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024
server.forward-headers-strategy=${SERVER_FORWARD_HEADERS:framework}

# ===============================================
# DATABASE CONFIGURATION
# ===============================================
spring.datasource.url=${DATABASE_URL:************************************************************************}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DATABASE_USERNAME:avnadmin}
spring.datasource.password=${DATABASE_PASSWORD:AVNS_c5RrhUSj3ipU33BiNYC}

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=${JPA_DDL_AUTO:update}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=${JPA_SHOW_SQL:false}
spring.jpa.properties.hibernate.format_sql=${JPA_FORMAT_SQL:false}

# ===============================================
# LOGGING CONFIGURATION
# ===============================================
logging.level.com.bright.chat=${LOG_LEVEL_APP:INFO}
logging.level.org.springframework.web=${LOG_LEVEL_WEB:WARN}
logging.level.org.springframework.security=${LOG_LEVEL_SECURITY:WARN}
logging.level.com.google.firebase=${LOG_LEVEL_FIREBASE:WARN}
logging.level.com.google.cloud=${LOG_LEVEL_CLOUD:WARN}
logging.level.org.springframework.messaging=${LOG_LEVEL_MESSAGING:WARN}

# File logging (production)
logging.pattern.file=${LOG_PATTERN_FILE:%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n}
logging.pattern.console=${LOG_PATTERN_CONSOLE:%d{yyyy-MM-dd HH:mm:ss} - %msg%n}
logging.file.name=${LOG_FILE_NAME:}
logging.file.max-size=${LOG_FILE_MAX_SIZE:10MB}
logging.file.max-history=${LOG_FILE_MAX_HISTORY:30}

# ===============================================
# RABBITMQ CONFIGURATION
# ===============================================
spring.rabbitmq.host=${RABBITMQ_HOST:dingo.rmq.cloudamqp.com}
spring.rabbitmq.virtual-host=${RABBITMQ_VHOST:tttiyfjl}
spring.rabbitmq.username=${RABBITMQ_USERNAME:tttiyfjl}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:FpBQxeRW_ba3WN4Ht3NNSP0Pyaw_fHkY}
spring.rabbitmq.dynamic=true

# ===============================================
# EMAIL CONFIGURATION
# ===============================================
spring.mail.username=${MAIL_USERNAME:<EMAIL>}
spring.mail.password=${MAIL_PASSWORD:xtoo qwbn snho lmeu}
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:465}
spring.mail.protocol=${MAIL_PROTOCOL:smtps}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

# ===============================================
# REDIS CONFIGURATION
# ===============================================
spring.redis.host=${REDIS_HOST:valkey-d1efab5-hunghvhpu-2edc.l.aivencloud.com}
spring.redis.port=${REDIS_PORT:19936}
spring.redis.password=${REDIS_PASSWORD:AVNS_Lj_clPvsloOEtcHD9oA}
spring.redis.database=${REDIS_DATABASE:1}
spring.redis.timeout=${REDIS_TIMEOUT:2000}
spring.redis.lettuce.pool.max-active=${REDIS_POOL_MAX_ACTIVE:8}
spring.redis.lettuce.pool.max-idle=${REDIS_POOL_MAX_IDLE:8}
spring.redis.lettuce.pool.min-idle=${REDIS_POOL_MIN_IDLE:0}
spring.redis.lettuce.pool.max-wait=${REDIS_POOL_MAX_WAIT:-1ms}

# Redis URL (alternative configuration)
spring.data.redis.url=${REDIS_URL:rediss://default:<EMAIL>:19936}

# ===============================================
# CACHE CONFIGURATION
# ===============================================
spring.cache.type=${CACHE_TYPE:redis}
spring.cache.redis.time-to-live=${CACHE_TTL:1800000}
spring.cache.redis.cache-null-values=false

# ===============================================
# FILE UPLOAD CONFIGURATION
# ===============================================
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=${UPLOAD_MAX_FILE_SIZE:10MB}
spring.servlet.multipart.max-request-size=${UPLOAD_MAX_REQUEST_SIZE:10MB}

# Filebase S3 Configuration
filebase.accessKey=${FILEBASE_ACCESS_KEY:47C32A2E3FFCE32C1BDA}
filebase.secretKey=${FILEBASE_SECRET_KEY:GxN7Z9CniDTpYFIwFxqwLFthRC0pfaPsn4MMxkTn}
filebase.bucket=${FILEBASE_BUCKET:favi}
filebase.endpoint=${FILEBASE_ENDPOINT:https://s3.filebase.com}

# ===============================================
# FIREBASE CONFIGURATION
# ===============================================
firebase.enabled=${FIREBASE_ENABLED:true}
firebase.service-account-key=${FIREBASE_SERVICE_ACCOUNT_KEY:firebase/serviceAccountKey.json}
firebase.project-id=${FIREBASE_PROJECT_ID:your-firebase-project-id}
firebase.database-url=${FIREBASE_DATABASE_URL:https://your-firebase-project-id-default-rtdb.firebaseio.com/}

# ===============================================
# JACKSON/JSON CONFIGURATION
# ===============================================
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=UTC

# ===============================================
# WEBSOCKET CONFIGURATION
# ===============================================
spring.websocket.allowed-origins=${WEBSOCKET_ALLOWED_ORIGINS:*}

# ===============================================
# CHAT APPLICATION CONFIGURATION
# ===============================================
app.name=Bright Chat Service
app.version=1.0.0
chat.max-message-length=${CHAT_MAX_MESSAGE_LENGTH:4000}
chat.max-group-members=${CHAT_MAX_GROUP_MEMBERS:500}
chat.file-upload.max-size=${CHAT_FILE_UPLOAD_MAX_SIZE:50MB}
chat.file-upload.allowed-types=${CHAT_FILE_UPLOAD_ALLOWED_TYPES:image/jpeg,image/png,image/gif,video/mp4,audio/mpeg,application/pdf}

# ===============================================
# SWAGGER/OPENAPI CONFIGURATION
# ===============================================
springdoc.api-docs.enabled=${SWAGGER_API_DOCS_ENABLED:true}
springdoc.swagger-ui.enabled=${SWAGGER_UI_ENABLED:true}
springdoc.swagger-ui.path=${SWAGGER_UI_PATH:/swagger-ui.html}
springdoc.api-docs.path=${SWAGGER_API_DOCS_PATH:/api-docs}
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true

# ===============================================
# MICROSERVICE COMMUNICATION
# ===============================================
user-service.base-url=${USER_SERVICE_BASE_URL:https://bright-user-beta.vercel.app}
user-service.timeout.connect=${USER_SERVICE_TIMEOUT_CONNECT:5000}
user-service.timeout.read=${USER_SERVICE_TIMEOUT_READ:10000}

# ===============================================
# CORS CONFIGURATION
# ===============================================
cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001,https://bright-user-beta.vercel.app,https://bright-chat.vercel.app,https://*.bright-chat.com}
cors.allowed-methods=${CORS_ALLOWED_METHODS:GET,POST,PUT,DELETE,OPTIONS,PATCH}
cors.allowed-headers=${CORS_ALLOWED_HEADERS:*}
cors.exposed-headers=${CORS_EXPOSED_HEADERS:X-Total-Count,X-Page-Count,X-User-Id,Authorization}
cors.allow-credentials=${CORS_ALLOW_CREDENTIALS:true}
cors.max-age=${CORS_MAX_AGE:3600}

# ===============================================
# SCHEDULER CONFIGURATION
# ===============================================
scheduler.cron.expression=${SCHEDULER_CRON:0 55 21 * * ?}

# ===============================================
# ACTUATOR CONFIGURATION
# ===============================================
management.endpoints.web.exposure.include=${ACTUATOR_ENDPOINTS:health,info,metrics}
management.endpoint.health.show-details=${ACTUATOR_HEALTH_DETAILS:when-authorized}
management.endpoints.web.base-path=${ACTUATOR_BASE_PATH:/actuator}
management.security.enabled=${ACTUATOR_SECURITY_ENABLED:true}

# ===============================================
# DEVELOPMENT TOOLS (DEV PROFILE ONLY)
# ===============================================
spring.devtools.restart.enabled=${DEVTOOLS_RESTART_ENABLED:false}
spring.devtools.livereload.enabled=${DEVTOOLS_LIVERELOAD_ENABLED:false}

# ===============================================
# TEST CONFIGURATION
# ===============================================
spring.test.database.replace=${TEST_DATABASE_REPLACE:auto}
spring.autoconfigure.exclude=${AUTOCONFIGURE_EXCLUDE:}

# ===============================================
# SECURITY CONFIGURATION
# ===============================================
server.ssl.enabled=${SSL_ENABLED:false}

# ===============================================
# PROFILE-SPECIFIC OVERRIDES
# ===============================================

# Development Profile Overrides
%dev.server.port=8080
%dev.logging.level.com.bright.chat=DEBUG
%dev.logging.level.org.springframework.web=DEBUG
%dev.logging.level.org.springframework.security=DEBUG
%dev.logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
%dev.cors.allowed-origins=http://localhost:3000,http://localhost:3001,http://localhost:4200,http://localhost:8080,http://localhost:8081
%dev.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH,HEAD
%dev.cors.exposed-headers=X-Total-Count,X-Page-Count,X-User-Id,Authorization,Content-Disposition
%dev.firebase.service-account-key=firebase/serviceAccountKey-dev.json
%dev.firebase.project-id=bright-chat-dev
%dev.firebase.database-url=https://bright-chat-dev-default-rtdb.firebaseio.com/
%dev.user-service.base-url=http://localhost:8081
%dev.spring.websocket.allowed-origins=http://localhost:3000,http://localhost:3001,http://localhost:4200
%dev.spring.devtools.restart.enabled=true
%dev.spring.devtools.livereload.enabled=true
%dev.spring.redis.host=localhost
%dev.spring.redis.port=6379
%dev.spring.redis.password=
%dev.spring.redis.database=0
%dev.spring.cache.redis.time-to-live=600000

# Production Profile Overrides
%prod.server.compression.enabled=true
%prod.logging.level.com.bright.chat=INFO
%prod.logging.level.org.springframework.web=WARN
%prod.logging.level.org.springframework.security=WARN
%prod.logging.level.com.google.firebase=WARN
%prod.logging.level.com.google.cloud=WARN
%prod.logging.level.org.springframework.messaging=WARN
%prod.logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
%prod.logging.file.name=logs/bright-chat.log
%prod.logging.file.max-size=10MB
%prod.logging.file.max-history=30
%prod.cors.allowed-origins=https://bright-chat.com,https://www.bright-chat.com,https://app.bright-chat.com,https://chat.bright-chat.com
%prod.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
%prod.cors.allowed-headers=Content-Type,Authorization,X-User-Id,X-Requested-With,Accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
%prod.cors.exposed-headers=X-Total-Count,X-Page-Count,X-User-Id
%prod.cors.max-age=86400
%prod.firebase.service-account-key=firebase/serviceAccountKey-prod.json
%prod.firebase.project-id=bright-chat-prod
%prod.firebase.database-url=https://bright-chat-prod-default-rtdb.firebaseio.com/
%prod.user-service.base-url=https://user-service.bright-chat.com
%prod.user-service.timeout.connect=3000
%prod.user-service.timeout.read=8000
%prod.spring.websocket.allowed-origins=https://bright-chat.com,https://www.bright-chat.com,https://app.bright-chat.com
%prod.server.forward-headers-strategy=native
%prod.spring.jpa.show-sql=false
%prod.spring.jpa.properties.hibernate.format_sql=false
%prod.springdoc.api-docs.enabled=false
%prod.springdoc.swagger-ui.enabled=false
%prod.spring.redis.timeout=3000
%prod.spring.redis.lettuce.pool.max-active=20
%prod.spring.redis.lettuce.pool.max-idle=10
%prod.spring.redis.lettuce.pool.min-idle=5
%prod.spring.redis.lettuce.pool.max-wait=2000ms
%prod.spring.cache.redis.time-to-live=3600000

# Test Profile Overrides
%test.server.port=0
%test.logging.level.com.bright.chat=WARN
%test.logging.level.org.springframework.web=WARN
%test.logging.level.org.springframework.security=WARN
%test.logging.level.com.google.firebase=ERROR
%test.logging.level.com.google.cloud=ERROR
%test.logging.level.org.springframework.messaging=WARN
%test.cors.allowed-origins=*
%test.cors.allowed-methods=*
%test.cors.allowed-headers=*
%test.cors.exposed-headers=*
%test.firebase.enabled=false
%test.firebase.service-account-key=firebase/serviceAccountKey-test.json
%test.firebase.project-id=bright-chat-test
%test.firebase.database-url=https://bright-chat-test-default-rtdb.firebaseio.com/
%test.user-service.base-url=http://localhost:8082
%test.user-service.timeout.connect=1000
%test.user-service.timeout.read=3000
%test.spring.websocket.allowed-origins=*
%test.spring.test.database.replace=none
%test.spring.jpa.hibernate.ddl-auto=create-drop
%test.spring.jpa.show-sql=false
%test.springdoc.api-docs.enabled=false
%test.springdoc.swagger-ui.enabled=false
%test.spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
%test.spring.cache.type=none
%test.spring.redis.host=localhost
%test.spring.redis.port=6379
%test.spring.redis.timeout=1000

