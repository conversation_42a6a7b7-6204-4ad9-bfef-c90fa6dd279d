spring.application.name=chat
spring.profiles.active=default
logging.level.org.springframework.web=DEBUG
server.port=${port:8080}
spring.main.lazy-initialization=false
spring.datasource.url=************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=avnadmin
spring.datasource.password=AVNS_c5RrhUSj3ipU33BiNYC
spring.jpa.hibernate.ddl-auto=update
# for Spring Boot 3
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
# for rabbit MQ
spring.rabbitmq.host=dingo.rmq.cloudamqp.com
spring.rabbitmq.virtual-host=tttiyfjl
spring.rabbitmq.username=tttiyfjl
spring.rabbitmq.password=FpBQxeRW_ba3WN4Ht3NNSP0Pyaw_fHkY
spring.rabbitmq.dynamic=true
# email config
spring.mail.username=<EMAIL>
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.host=smtp.gmail.com
spring.mail.port=465
spring.mail.protocol=smtps
spring.mail.password=xtoo qwbn snho lmeu
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
#cloudinary Config
#cloudinary.cloud_name=dwydl3lof
#cloudinary.api_key=669573841645489
#cloudinary.api_secret=aiRWQuCu1VON9oJs6QO2t9LEfqo
# Customize API Info
#logging.level.org.springframework=DEBUG
scheduler.cron.expression=0 55 21 * * ?
#Redis config
spring.data.redis.url=rediss://default:<EMAIL>:19936
# File Upload
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
filebase.accessKey=47C32A2E3FFCE32C1BDA
filebase.secretKey=GxN7Z9CniDTpYFIwFxqwLFthRC0pfaPsn4MMxkTn
filebase.bucket=favi
filebase.endpoint=https://s3.filebase.com

# Firebase Configuration
firebase.service-account-key=firebase/serviceAccountKey.json
firebase.project-id=your-firebase-project-id
firebase.database-url=https://your-firebase-project-id-default-rtdb.firebaseio.com/

# Jackson Configuration for LocalDateTime support
spring.jackson.serialization.write-dates-as-timestamps=false
spring.jackson.time-zone=UTC

# WebSocket Configuration
spring.websocket.allowed-origins=*

# Chat Application Configuration
app.name=Bright Chat Service
app.version=1.0.0
chat.max-message-length=4000
chat.max-group-members=500
chat.file-upload.max-size=50MB
chat.file-upload.allowed-types=image/jpeg,image/png,image/gif,video/mp4,audio/mpeg,application/pdf

# Swagger/OpenAPI Configuration
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true

# Microservice Communication
user-service.base-url=https://bright-user-beta.vercel.app
user-service.timeout.connect=5000
user-service.timeout.read=10000

# CORS Configuration
cors.allowed-origins=http://localhost:3000,http://localhost:3001,https://bright-user-beta.vercel.app,https://bright-chat.vercel.app,https://*.bright-chat.com
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS,PATCH
cors.allowed-headers=*
cors.exposed-headers=X-Total-Count,X-Page-Count,X-User-Id,Authorization
cors.allow-credentials=true
cors.max-age=3600

# Redis Configuration
spring.redis.host=valkey-d1efab5-hunghvhpu-2edc.l.aivencloud.com
spring.redis.port=19936
spring.redis.password=AVNS_Lj_clPvsloOEtcHD9oA
spring.redis.database=1
spring.redis.timeout=2000
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.pool.max-wait=-1ms

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=1800000
spring.cache.redis.cache-null-values=false

