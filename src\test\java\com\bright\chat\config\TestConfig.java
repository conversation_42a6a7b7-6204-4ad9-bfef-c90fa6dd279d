package com.bright.chat.config;

import com.bright.chat.service.FirestoreService;
import com.bright.chat.service.UserServiceClient;
import com.google.cloud.firestore.Firestore;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * Test configuration to mock external dependencies
 */
@TestConfiguration
@Profile("test")
public class TestConfig {

    /**
     * Mock Firestore for tests
     */
    @Bean
    @Primary
    public Firestore mockFirestore() {
        return Mockito.mock(Firestore.class);
    }

    /**
     * Mock FirestoreService for tests
     */
    @Bean
    @Primary
    public FirestoreService mockFirestoreService() {
        return Mockito.mock(FirestoreService.class);
    }

    /**
     * Mock UserServiceClient for tests
     */
    @Bean
    @Primary
    public UserServiceClient mockUserServiceClient() {
        return Mockito.mock(UserServiceClient.class);
    }
}
